const path = require("path");
const fs = require("fs");
const { renderMedia, selectComposition } = require("@remotion/renderer");
const { cacheMultipleVideos, cleanupCachedVideos } = require("../utils/videoCache");

// Helper function to get video duration from URL or file
async function getVideoDuration(videoPath, originalUrl = null) {
  console.log(`🔍 Attempting to get duration for: ${videoPath}`);

  // For remote URLs, try multiple approaches
  if (videoPath.startsWith('http')) {
    // Try ffprobe first with timeout and better error handling
    const duration = await tryFFProbe(videoPath);
    if (duration > 0) {
      console.log(`✅ FFProbe detected duration: ${duration}s for ${videoPath}`);
      return duration;
    }

    // If ffprobe fails, use intelligent fallbacks based on URL patterns
    console.warn(`⚠️ FFProbe failed for ${videoPath}, using intelligent fallback`);

    if (videoPath.includes('0x0.st')) {
      // 0x0.st clips are typically very short user uploads
      const fallbackDuration = 2.5; // Slightly longer fallback
      console.log(`📏 Using ${fallbackDuration}s fallback for 0x0.st clip`);
      return fallbackDuration;
    } else if (videoPath.includes('pexels.com')) {
      const fallbackDuration = 4; // Pexels clips are usually short
      console.log(`📏 Using ${fallbackDuration}s fallback for Pexels clip`);
      return fallbackDuration;
    } else {
      const fallbackDuration = 3; // Generic remote fallback
      console.log(`📏 Using ${fallbackDuration}s fallback for remote clip`);
      return fallbackDuration;
    }
  } else {
    // For local files (including cached videos), try ffprobe but use smarter fallbacks
    const duration = await tryFFProbe(videoPath);
    if (duration > 0) {
      console.log(`✅ Local file duration detected: ${duration}s for ${videoPath}`);
      return duration;
    }

    // If ffprobe is not available, try JavaScript-based duration detection
    console.warn(`⚠️ FFProbe not available for ${videoPath}, trying file-size estimation`);

    // For cached videos, try to get a better estimate using file size
    if (!videoPath.startsWith('http')) {
      // Try to get the absolute path for file size analysis
      let absolutePath = videoPath;
      if (!path.isAbsolute(videoPath)) {
        // Convert relative path back to absolute for file analysis
        const publicDir = path.join(process.cwd(), 'public');
        absolutePath = path.join(publicDir, videoPath);
      }

      const estimatedDuration = await getVideoDurationJS(absolutePath);
      console.log(`📊 File-size estimated duration: ${estimatedDuration}s for ${videoPath}`);
      return estimatedDuration;
    }

    // Fallback to URL-based estimation for remote files
    if (originalUrl) {
      if (originalUrl.includes('0x0.st')) {
        const fallbackDuration = 2.5;
        console.log(`📏 Using ${fallbackDuration}s URL-based fallback for 0x0.st clip`);
        return fallbackDuration;
      } else if (originalUrl.includes('pexels.com')) {
        const fallbackDuration = 4;
        console.log(`📏 Using ${fallbackDuration}s URL-based fallback for Pexels clip`);
        return fallbackDuration;
      }
    }

    // Generic fallback for local files
    const fallbackDuration = 3;
    console.log(`📏 Using ${fallbackDuration}s generic fallback for local file`);
    return fallbackDuration;
  }
}

// JavaScript-based video duration detection (fallback when ffprobe not available)
async function getVideoDurationJS(videoPath) {
  const fs = require('fs');

  try {
    // For cached videos, try to read the file size and estimate duration
    if (fs.existsSync(videoPath)) {
      const stats = fs.statSync(videoPath);
      const fileSizeKB = stats.size / 1024;

      // Rough estimation based on file size (very approximate)
      // 0x0.st videos are typically 30-100KB for 1-3 seconds
      if (fileSizeKB < 50) {
        return 1.5; // Very small file, likely 1-2 seconds
      } else if (fileSizeKB < 150) {
        return 2.5; // Medium file, likely 2-3 seconds
      } else if (fileSizeKB < 500) {
        return 4.0; // Larger file, likely 3-5 seconds
      } else {
        return 6.0; // Large file, likely 5+ seconds
      }
    }
  } catch (error) {
    console.warn(`⚠️ File size estimation failed for ${videoPath}: ${error.message}`);
  }

  return 2.5; // Default fallback
}

// Separate function to try ffprobe with proper timeout and error handling
async function tryFFProbe(videoPath) {
  const { spawn } = require('child_process');

  return new Promise((resolve) => {
    const timeout = 10000; // 10 second timeout
    let timedOut = false;

    const ffprobe = spawn('ffprobe', [
      '-v', 'quiet',
      '-print_format', 'json',
      '-show_format',
      '-show_streams',
      '-timeout', '8000000', // 8 second timeout for network operations
      videoPath
    ]);

    let output = '';
    let errorOutput = '';

    // Set up timeout
    const timeoutId = setTimeout(() => {
      timedOut = true;
      ffprobe.kill('SIGKILL');
      console.warn(`⏰ FFProbe timeout for ${videoPath}`);
      resolve(0); // Return 0 to indicate failure
    }, timeout);

    ffprobe.stdout.on('data', (data) => {
      output += data.toString();
    });

    ffprobe.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    ffprobe.on('close', (code) => {
      if (timedOut) return;
      clearTimeout(timeoutId);

      try {
        if (code === 0 && output) {
          const metadata = JSON.parse(output);
          const duration = parseFloat(metadata.format.duration);
          if (duration && duration > 0) {
            resolve(duration);
            return;
          }
        }
      } catch (error) {
        console.warn(`❌ Failed to parse ffprobe output for ${videoPath}: ${error.message}`);
        if (errorOutput) {
          console.warn(`FFProbe stderr: ${errorOutput.substring(0, 200)}`);
        }
      }

      resolve(0); // Return 0 to indicate failure
    });

    ffprobe.on('error', (error) => {
      if (timedOut) return;
      clearTimeout(timeoutId);
      console.warn(`❌ FFProbe spawn error for ${videoPath}: ${error.message}`);
      resolve(0); // Return 0 to indicate failure
    });
  });
}

/**
 * Renders video using Remotion
 * @param {Object} params
 * @param {Array} params.clips - Array of video URLs or local filenames
 * @param {string} params.voiceOver - Audio URL or local filename (optional)
 * @param {string} params.music - Audio URL or local filename (optional)
 * @param {string} params.aspectRatio - Aspect ratio: '16:9', '9:16', '1:1', '4:3', 'auto' (optional)
 * @param {string} params.transition - Transition type: 'fade', 'slide', 'zoom', 'none' (optional)
 * @param {Array} params.subtitles - Array of subtitle objects {text, start, end}
 * @param {Array} params.overlays - Array of overlay objects {src, start, end, position, etc.}
 * @param {string} params.tempDir - Temp folder path
 * @param {Array} params.tempFiles - Array to push temp files (optional)
 * @returns {Promise<string>} Path to rendered video
 */
async function renderVideo({ clips, clipDurations = [], voiceOver, voiceOverVolume = 1.0, music, musicVolume = 0.3, aspectRatio = 'auto', transition = 'fade', subtitles, overlays, tempDir, tempFiles = [], outputPath }) {
  // Ensure output path is provided
  if (!outputPath) {
    outputPath = path.join(tempDir, `final-${Date.now()}.mp4`);
  }

  // Create cache directory for remote videos in both public/ and build/public/ for staticFile() compatibility
  const publicDir = path.join(process.cwd(), 'public');
  const buildPublicDir = path.join(process.cwd(), 'build', 'public');
  const cacheDir = path.join(publicDir, 'video-cache');
  const buildCacheDir = path.join(buildPublicDir, 'video-cache');
  const cachedVideos = [];

  // Ensure all directories exist
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }
  if (!fs.existsSync(cacheDir)) {
    fs.mkdirSync(cacheDir, { recursive: true });
  }
  if (!fs.existsSync(buildPublicDir)) {
    fs.mkdirSync(buildPublicDir, { recursive: true });
  }
  if (!fs.existsSync(buildCacheDir)) {
    fs.mkdirSync(buildCacheDir, { recursive: true });
  }

  // Separate remote URLs from local files
  const remoteUrls = [];
  const localFiles = [];

  clips.forEach((clip, index) => {
    if (typeof clip === 'string') {
      if (clip.startsWith('http://') || clip.startsWith('https://')) {
        remoteUrls.push({ url: clip, index });
      } else {
        localFiles.push({ path: clip, index });
      }
    } else {
      const clipPath = clip.localPath || clip.url || clip;
      if (clipPath.startsWith('http://') || clipPath.startsWith('https://')) {
        remoteUrls.push({ url: clipPath, index });
      } else {
        localFiles.push({ path: clipPath, index });
      }
    }
  });

  console.log(`📊 Video processing summary:`);
  console.log(`   🌐 Remote URLs to cache: ${remoteUrls.length}`);
  console.log(`   📁 Local files: ${localFiles.length}`);

  // Cache remote videos for reliable Remotion access
  let cacheResults = [];
  if (remoteUrls.length > 0) {
    console.log(`📥 Caching remote videos to avoid Remotion timeouts...`);
    cacheResults = await cacheMultipleVideos(
      remoteUrls.map(r => r.url),
      cacheDir
    );
  }

  // Build final clip paths array with cached videos
  const clipPaths = new Array(clips.length);

  // Build mapping for original URLs to help with duration detection
  const originalUrlMap = {};

  // Add cached remote videos and copy to build directory
  remoteUrls.forEach((remote, i) => {
    const cacheResult = cacheResults[i];
    if (cacheResult && cacheResult.cachedPath) {
      // Get the filename from the cached path
      const filename = path.basename(cacheResult.cachedPath);
      const buildCachedPath = path.join(buildCacheDir, filename);

      try {
        // Copy the cached video to build/public/video-cache for Remotion access
        fs.copyFileSync(cacheResult.cachedPath, buildCachedPath);
        console.log(`📋 Copied cached video to build directory: ${buildCachedPath}`);
      } catch (copyError) {
        console.warn(`⚠️ Failed to copy video to build directory: ${copyError.message}`);
      }

      // Use relative path for staticFile() with proper forward slashes
      const webPath = `video-cache/${filename}`;
      clipPaths[remote.index] = webPath;
      cachedVideos.push(cacheResult.cachedPath);
      cachedVideos.push(buildCachedPath); // Also track build copy for cleanup

      // Store original URL for duration detection fallback
      originalUrlMap[webPath] = remote.url;
      console.log(`✅ Using cached video for clip ${remote.index + 1}: ${webPath}`);
    } else {
      // Fallback to original URL if caching failed
      clipPaths[remote.index] = remote.url;
      console.warn(`⚠️ Caching failed for clip ${remote.index + 1}, using original URL: ${remote.url}`);
    }
  });

  // Add local files
  localFiles.forEach(local => {
    clipPaths[local.index] = local.path;
    console.log(`📁 Using local file for clip ${local.index + 1}: ${local.path}`);
  });

  // Get durations for each clip (use provided durations or detect)
  console.log('🎬 Processing video durations...');
  const finalClipDurations = [];

  for (let i = 0; i < clipPaths.length; i++) {
    const clipPath = clipPaths[i];
    console.log(`📹 Processing clip ${i + 1}/${clipPaths.length}: ${clipPath}`);

    let duration;

    // Check if user provided duration for this clip
    if (clipDurations && clipDurations[i] && clipDurations[i] > 0) {
      duration = clipDurations[i];
      console.log(`✅ Using provided duration: ${duration}s for clip ${i + 1}`);
    } else {
      // Fall back to detection if no duration provided
      console.log(`🔍 No duration provided, detecting for clip ${i + 1}...`);
      const originalUrl = originalUrlMap[clipPath];
      duration = await getVideoDuration(clipPath, originalUrl);
      console.log(`📊 Detected duration: ${duration}s for clip ${i + 1}`);
    }

    finalClipDurations.push(duration);
    console.log(`⏱️  Clip ${i + 1} final duration: ${duration}s`);
  }

  // Calculate total duration - DON'T cap it, use actual durations
  const totalDurationSeconds = finalClipDurations.reduce((sum, duration) => sum + duration, 0);

  // Check if we have subtitles to determine if we should use subtitle timing instead
  let finalDurationSeconds = totalDurationSeconds;

  if (subtitles && subtitles.length > 0) {
    // Find the latest subtitle end time
    const latestSubtitleEnd = Math.max(...subtitles.map(sub => sub.end));
    console.log(`📝 Latest subtitle ends at: ${latestSubtitleEnd}s`);
    console.log(`🎞️  Total clip duration: ${totalDurationSeconds}s`);

    // Use the longer of subtitle timing or clip duration, but prefer subtitle timing for better sync
    if (latestSubtitleEnd > 0) {
      finalDurationSeconds = Math.max(latestSubtitleEnd, totalDurationSeconds);
      console.log(`🎯 Using final duration: ${finalDurationSeconds}s (based on subtitle timing)`);
    }
  }

  const durationInFrames = Math.round(finalDurationSeconds * 30); // Convert to frames at 30fps

  console.log(`🎬 FINAL VIDEO SETTINGS:`);
  console.log(`   📊 Individual clip durations: ${finalClipDurations.map(d => `${d}s`).join(', ')}`);
  console.log(`   ⏱️  Total clip duration: ${totalDurationSeconds}s`);
  console.log(`   🎯 Final video duration: ${finalDurationSeconds}s (${durationInFrames} frames)`);

  const serveUrl = path.resolve(process.cwd(), "build"); // Use bundled version

  try {
    // First, get the composition with proper configuration
    const inputProps = {
      clips: clipPaths,
      clipDurations: finalClipDurations, // Pass the final processed durations
      voiceOver: voiceOver || null,
      voiceOverVolume: voiceOverVolume, // Pass voice-over volume
      music: music || null,
      musicVolume: musicVolume, // Pass music volume
      aspectRatio: aspectRatio || 'auto',
      transition: transition || 'fade',
      subtitles: subtitles || [],
      overlays: overlays || [],
    };

    const composition = await selectComposition({
      serveUrl,
      id: "VideoMerger",
      inputProps,
    });

    // Override the duration to match our calculated duration
    const compositionWithDuration = {
      ...composition,
      durationInFrames,
    };

    console.log(`🎬 Composition settings:`);
    console.log(`   📐 Resolution: ${composition.width}x${composition.height}`);
    console.log(`   🎞️  Duration: ${durationInFrames} frames (${finalDurationSeconds}s)`);
    console.log(`   🎨 Aspect Ratio: ${aspectRatio}`);

    await renderMedia({
      composition: compositionWithDuration,
      serveUrl,
      codec: "h264",
      outputLocation: outputPath,
      inputProps,
      overwrite: true,
      // Increase timeout to handle video loading
      timeoutInMilliseconds: 60000, // 60 seconds
      // ANTI-STUTTER: Enhanced Chromium options for smooth video playback
      chromiumOptions: {
        args: [
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          // Additional flags to improve video performance
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--disable-background-media-suspend',
          '--autoplay-policy=no-user-gesture-required',
          // Memory and performance optimizations
          '--max_old_space_size=4096',
          '--no-sandbox',
          // Video-specific optimizations
          '--enable-features=VaapiVideoDecoder',
          '--use-gl=desktop',
        ],
      },
      // Additional rendering options for smoother playback
      concurrency: 1, // Render one frame at a time to reduce memory pressure
    });

    console.log(`✅ Video rendering completed successfully`);

    // Clean up cached videos after successful render
    if (cachedVideos.length > 0) {
      console.log(`🧹 Cleaning up ${cachedVideos.length} cached videos...`);
      cleanupCachedVideos(cachedVideos);
    }

    return outputPath;
  } catch (error) {
    console.error("❌ Remotion render error:", error);

    // Clean up cached videos on error too
    if (cachedVideos.length > 0) {
      console.log(`🧹 Cleaning up ${cachedVideos.length} cached videos after error...`);
      cleanupCachedVideos(cachedVideos);
    }

    // Provide more specific error messages
    if (error.message.includes('delayRender')) {
      throw new Error(`Video loading timeout: One or more videos failed to load within the timeout period. This often happens with slow or unreliable remote video URLs. Error: ${error.message}`);
    } else if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
      throw new Error(`Network error: Unable to access remote video URLs. Please check your internet connection and verify the video URLs are accessible. Error: ${error.message}`);
    } else {
      throw new Error(`Video rendering failed: ${error.message}`);
    }
  }
}

module.exports = { renderVideo };
