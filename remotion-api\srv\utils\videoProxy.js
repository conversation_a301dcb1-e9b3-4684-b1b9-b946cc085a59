const express = require('express');
const https = require('https');
const http = require('http');

/**
 * Creates a video proxy middleware that serves remote videos locally
 * This helps avoid Remotion timeout issues with remote URLs
 */
function createVideoProxy() {
  const router = express.Router();
  
  router.get('/proxy-video', (req, res) => {
    const videoUrl = req.query.url;
    
    if (!videoUrl) {
      return res.status(400).json({ error: 'Missing url parameter' });
    }
    
    console.log(`🔄 Proxying video: ${videoUrl}`);
    
    // Choose http or https based on URL
    const client = videoUrl.startsWith('https:') ? https : http;
    
    const proxyRequest = client.get(videoUrl, {
      timeout: 30000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    }, (proxyResponse) => {
      // <PERSON>le redirects
      if (proxyResponse.statusCode >= 300 && proxyResponse.statusCode < 400 && proxyResponse.headers.location) {
        console.log(`🔄 Following redirect to: ${proxyResponse.headers.location}`);
        return res.redirect(`/proxy-video?url=${encodeURIComponent(proxyResponse.headers.location)}`);
      }
      
      if (proxyResponse.statusCode !== 200) {
        console.error(`❌ Proxy error: HTTP ${proxyResponse.statusCode}`);
        return res.status(proxyResponse.statusCode).json({ 
          error: `Failed to fetch video: HTTP ${proxyResponse.statusCode}` 
        });
      }
      
      // Set appropriate headers
      res.set({
        'Content-Type': proxyResponse.headers['content-type'] || 'video/mp4',
        'Content-Length': proxyResponse.headers['content-length'],
        'Accept-Ranges': 'bytes',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      });
      
      // Handle range requests for video seeking
      if (req.headers.range && proxyResponse.headers['accept-ranges'] === 'bytes') {
        res.set('Accept-Ranges', 'bytes');
      }
      
      console.log(`✅ Streaming video: ${videoUrl}`);
      proxyResponse.pipe(res);
    });
    
    proxyRequest.on('timeout', () => {
      console.error(`⏰ Proxy timeout for ${videoUrl}`);
      if (!res.headersSent) {
        res.status(504).json({ error: 'Video proxy timeout' });
      }
    });
    
    proxyRequest.on('error', (error) => {
      console.error(`❌ Proxy error for ${videoUrl}: ${error.message}`);
      if (!res.headersSent) {
        res.status(500).json({ error: `Proxy error: ${error.message}` });
      }
    });
    
    // Handle client disconnect
    req.on('close', () => {
      proxyRequest.destroy();
    });
  });
  
  return router;
}

/**
 * Converts a remote video URL to a local proxy URL
 * @param {string} remoteUrl - Remote video URL
 * @param {string} serverHost - Local server host (e.g., 'localhost:5001')
 * @returns {string} - Local proxy URL
 */
function getProxyUrl(remoteUrl, serverHost = 'localhost:5001') {
  if (!remoteUrl.startsWith('http')) {
    return remoteUrl; // Already local
  }
  
  return `http://${serverHost}/proxy-video?url=${encodeURIComponent(remoteUrl)}`;
}

module.exports = {
  createVideoProxy,
  getProxyUrl
};
