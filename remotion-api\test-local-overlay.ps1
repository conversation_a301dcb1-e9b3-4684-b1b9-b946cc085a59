# Test with local SVG file
$body = @{
    clips = @("https://0x0.st/8kgm.mp4")
    overlays = @(
        @{
            src = "test-logo.svg"
            start = 0
            end = 5
            position = "top-right"
            width = 120
            animation = "fade"
        }
    )
} | ConvertTo-Json -Depth 10

Write-Host "Testing local SVG overlay..."
Write-Host "Request body:"
Write-Host $body

$response = Invoke-RestMethod -Uri "http://localhost:5001/render" -Method POST -Body $body -ContentType "application/json"

Write-Host "Response:"
$response | ConvertTo-Json -Depth 10

Write-Host "Job ID: $($response.id)"
