const https = require('https');
const fs = require('fs');
const path = require('path');

// Download sample videos from the internet
async function downloadFile(url, filename) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(path.join(__dirname, 'public', filename));
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`✅ Downloaded: ${filename}`);
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(path.join(__dirname, 'public', filename), () => {});
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

async function downloadSampleVideos() {
  console.log('🎬 Downloading sample videos...');
  
  try {
    // Sample video URLs (small files for testing)
    const videos = [
      {
        url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
        filename: '8klQ.mp4'
      },
      {
        url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4', 
        filename: '8klP(1).mp4'
      }
    ];
    
    for (const video of videos) {
      try {
        await downloadFile(video.url, video.filename);
      } catch (error) {
        console.warn(`⚠️  Failed to download ${video.filename}: ${error.message}`);
      }
    }
    
    console.log('');
    console.log('🎉 Sample videos ready!');
    console.log('📁 Files in public directory:');
    
    const files = fs.readdirSync(path.join(__dirname, 'public'));
    files.forEach(file => {
      if (file.endsWith('.mp4')) {
        console.log(`   ✅ ${file}`);
      }
    });
    
    console.log('');
    console.log('🚀 Now you can test with your original command:');
    console.log('curl -X POST http://localhost:5001/render \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"clips": ["8klQ.mp4", "8klP(1).mp4"], "subtitles": [{"text": "Hello!", "start": 0, "end": 3}]}\'');
    
  } catch (error) {
    console.error('❌ Error downloading videos:', error.message);
    console.log('');
    console.log('💡 Alternative: Add your own MP4 files to the public/ directory');
    console.log('   - 8klQ.mp4');
    console.log('   - 8klP(1).mp4');
  }
}

downloadSampleVideos();
