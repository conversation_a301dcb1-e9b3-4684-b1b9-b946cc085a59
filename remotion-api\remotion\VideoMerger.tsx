import React, { useEffect } from "react";
import { TikTokTrendingSubtitle } from "./subtitles/TikTokTrendingSubtitle";
import { ArabicBannerSubtitle } from "./subtitles/ArabicBannerSubtitle";
import { AbsoluteFill, Sequence, Video, Audio, useVideoConfig, staticFile, interpolate, Easing, useCurrentFrame, Img } from "remotion";
import { z } from "zod";

export const VideoMerger = (props) => {
  const {
    clips = [],
    clipDurations = [],
    voiceOver,
    voiceOverVolume = 1.0,
    music,
    musicVolume = 0.3,
    aspectRatio = 'auto',
    transition = 'fade',
    subtitles = [],
    overlays = [],
  } = props;
  const { fps } = useVideoConfig();
  const frame = useCurrentFrame();
  useEffect(() => {
    clips.forEach((clip) => {
      if (clip.startsWith('http')) {
        // Preload remote videos if needed
      }
    });
  }, [clips]);
  const renderedSubtitles = [];
  let i = 0;
  while (i < subtitles.length) {
    const subtitle = subtitles[i];
    if (subtitle.style === 'arabic_banner' && i + 1 < subtitles.length && subtitles[i + 1].style === 'arabic_banner') {
      const top = subtitle;
      const bottom = subtitles[i + 1];
      renderedSubtitles.push(
        <Sequence
          key={`arabic-banner-top-${i}`}
          from={top.start * fps}
          durationInFrames={(bottom.end - top.start) * fps}
        >
          <AbsoluteFill style={{ justifyContent: 'flex-end', alignItems: 'center', paddingBottom: 100 }}>
            <ArabicBannerSubtitle
              text={top.text + '\n' + bottom.text}
              backgroundColor={top.backgroundColor}
              position={top.position || 'bottom'}
              animationFrame={frame - (top.start * fps)}
            />
          </AbsoluteFill>
        </Sequence>
      );
      i += 2;
      continue;
    }
    if (subtitle.style === 'arabic_banner') {
      renderedSubtitles.push(
        <Sequence
          key={`arabic-banner-single-${i}`}
          from={subtitle.start * fps}
          durationInFrames={(subtitle.end - subtitle.start) * fps}
        >
          <AbsoluteFill style={{ justifyContent: 'flex-end', alignItems: 'center', paddingBottom: 100 }}>
            <ArabicBannerSubtitle
              text={subtitle.text}
              backgroundColor={subtitle.backgroundColor}
              position={subtitle.position || 'bottom'}
              animationFrame={frame - (subtitle.start * fps)}
            />
          </AbsoluteFill>
        </Sequence>
      );
      i++;
      continue;
    }
    if (subtitle.style === 'tiktok_trending') {
      renderedSubtitles.push(
        <Sequence
          key={`subtitle-${i}`}
          from={subtitle.start * fps}
          durationInFrames={(subtitle.end - subtitle.start) * fps}
        >
          <AbsoluteFill style={{ justifyContent: 'flex-end', alignItems: 'center', paddingBottom: 100 }}>
            <TikTokTrendingSubtitle
              text={subtitle.text}
              backgroundColor={subtitle.backgroundColor}
              position={subtitle.position || 'bottom'}
              animationFrame={frame - (subtitle.start * fps)}
            />
          </AbsoluteFill>
        </Sequence>
      );
      i++;
      continue;
    }
    // Fallback for other styles
    renderedSubtitles.push(
      <Sequence
        key={`subtitle-${i}`}
        from={subtitle.start * fps}
        durationInFrames={(subtitle.end - subtitle.start) * fps}
      >
        <AbsoluteFill style={{ justifyContent: 'flex-end', alignItems: 'center', paddingBottom: 100 }}>
          <div style={{
            background: subtitle.backgroundColor || 'rgba(0,0,0,0.7)',
            color: subtitle.textColor || 'white',
            padding: '15px 25px',
            borderRadius: 12,
            fontSize: subtitle.fontSize || 48,
            fontWeight: 'bold',
            textAlign: 'center',
            maxWidth: '85vw',
            lineHeight: 1.2,
            fontFamily: 'Arial, sans-serif',
            textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
            border: '2px solid rgba(255,255,255,0.1)',
            boxShadow: '0 4px 20px rgba(0,0,0,0.3)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
          }}>
            {subtitle.text}
          </div>
        </AbsoluteFill>
      </Sequence>
    );
    i++;
  }
  return (
    <AbsoluteFill>
      {renderedSubtitles}
    </AbsoluteFill>
  );
};
            />
          </AbsoluteFill>
        </Sequence>
      );
      i++;
      continue;
    }
    // Fallback for other styles
    renderedSubtitles.push(
      <Sequence
        key={`subtitle-${i}`}
        from={subtitle.start * fps}
        durationInFrames={(subtitle.end - subtitle.start) * fps}
      >
        <AbsoluteFill style={{ justifyContent: 'flex-end', alignItems: 'center', paddingBottom: 100 }}>
          <div style={{
            background: subtitle.backgroundColor || 'rgba(0,0,0,0.7)',
            color: subtitle.textColor || 'white',
            padding: '15px 25px',
            borderRadius: 12,
            fontSize: subtitle.fontSize || 48,
            fontWeight: 'bold',
            textAlign: 'center',
            maxWidth: '85vw',
            lineHeight: 1.2,
            fontFamily: 'Arial, sans-serif',
            textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
            border: '2px solid rgba(255,255,255,0.1)',
            boxShadow: '0 4px 20px rgba(0,0,0,0.3)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
          }}>
            {subtitle.text}
          </div>
        </AbsoluteFill>
      </Sequence>
    );
    i++;
  }

  return (
    <AbsoluteFill>
      {/* ...existing video/audio overlays and other logic... */}
      {renderedSubtitles}
    </AbsoluteFill>
  );
};
        const overlayHeight = overlay.height || 200; // Default height as number
        const overlayOpacity = overlay.opacity || 1;
        const overlayScale = overlay.scale || 1;
        const overlayAnimation = overlay.animation || 'fade';
        const overlayAnimationDuration = overlay.animationDuration || 0.5;
        const offsetX = overlay.offsetX || 0;
        const offsetY = overlay.offsetY || 0;

        return (
          <Sequence
            key={`overlay-${index}`}
            from={overlay.start * fps}
            durationInFrames={(overlay.end - overlay.start) * fps}
          >
            <AbsoluteFill>
              <div
                style={{
                  position: 'absolute',
                  ...getOverlayPositionStyle(overlayPosition, overlayWidth, overlayHeight, offsetX, offsetY),
                  ...getOverlayAnimationStyle(
                    overlayAnimation,
                    frame,
                    overlay.start * fps,
                    overlay.end * fps,
                    overlayAnimationDuration
                  ),
                  opacity: overlayOpacity,
                  pointerEvents: 'none', // Prevent interaction
                }}
              >
                <Img
                  src={overlay.src.startsWith('http') ? overlay.src : staticFile(overlay.src)}
                  style={{
                    width: overlayWidth,
                    height: overlayHeight,
                    transform: `scale(${overlayScale})`,
                    objectFit: 'contain',
                    filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.3))', // Add subtle shadow
                  }}
                  onError={() => {
                    console.warn(`Failed to load overlay image: ${overlay.src}`);
                  }}
                />
              </div>
            </AbsoluteFill>
          </Sequence>
        );
      })}
    </AbsoluteFill>
  );
};