import React from "react";

/**
 * TikTokTrendingSubtitle
 * - Top line: white text, bold, black outline
 * - Bottom line: yellow text, bold, black outline
 * - Both lines: uppercase, heavy sans-serif, shadow, animated pop-in
 * - backgroundColor: optional (default: none)
 * - position: bottom (default)
 */
export const TikTokTrendingSubtitle: React.FC<{
  text: string;
  backgroundColor?: string;
  position?: "bottom" | "top" | "center";
  style?: React.CSSProperties;
  animationFrame?: number;
}> = ({
  text,
  backgroundColor,
  position = "bottom",
  style = {},
  animationFrame = 0,
}) => {
  // Support both '\n' and '\\n' as line breaks
  const cleanText = text.replace(/\\n/g, '\n');
  const [top, bottom] = cleanText.split("\n").length > 1
    ? cleanText.split("\n")
    : [cleanText, ""];

  // Animation: pop-in with scale and fade
  const anim = {
    transform: `scale(${0.8 + 0.2 * Math.min(animationFrame / 10, 1)})`,
    opacity: Math.min(animationFrame / 10, 1),
    transition: "all 0.4s cubic-bezier(.5,1.8,.5,1)",
  };

  // Default background for this style: none (text has outline)
  const containerStyle: React.CSSProperties = {
    position: "absolute",
    left: 0,
    right: 0,
    [position]: 60,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    pointerEvents: "none",
    ...style,
    ...anim,
  };

  // Font settings
  const fontFamily = "Impact, Arial Black, sans-serif";
  const fontWeight = 900;
  const fontSize = 64;
  const outline = "4px 4px 0 #000, -4px 4px 0 #000, 4px -4px 0 #000, -4px -4px 0 #000, 0 4px 0 #000, 4px 0 0 #000, 0 -4px 0 #000, -4px 0 0 #000";

  return (
    <div style={containerStyle}>
      <span
        style={{
          fontFamily,
          fontWeight,
          fontSize,
          color: "#fff",
          textTransform: "uppercase",
          WebkitTextStroke: "2px #000",
          textShadow: outline,
          letterSpacing: 1.5,
          lineHeight: 1.1,
          marginBottom: bottom ? 6 : 0,
          padding: backgroundColor ? "8px 24px" : 0,
          background: backgroundColor || undefined,
          borderRadius: backgroundColor ? 16 : 0,
        }}
      >
        {top}
      </span>
      {bottom && (
        <span
          style={{
            fontFamily,
            fontWeight,
            fontSize,
            color: "#ffe600",
            textTransform: "uppercase",
            WebkitTextStroke: "2px #000",
            textShadow: outline,
            letterSpacing: 1.5,
            lineHeight: 1.1,
            marginTop: 2,
            padding: backgroundColor ? "8px 24px" : 0,
            background: backgroundColor || undefined,
            borderRadius: backgroundColor ? 16 : 0,
          }}
        >
          {bottom}
        </span>
      )}
    </div>
  );
};

// Register for subtitle registry (if needed)
export const TikTokTrendingSubtitleMeta = {
  name: "tiktok_trending",
  displayName: "TikTok Trending",
  defaultBackground: undefined, // no background by default
  allowsBackground: true,
  defaultPosition: "bottom",
  animated: true,
};
