<!DOCTYPE html>
<html>
<head>
    <title>Create Test Logo</title>
</head>
<body>
    <canvas id="canvas" width="150" height="150"></canvas>
    <br>
    <button onclick="downloadLogo()">Download Test Logo</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Create a simple logo with transparent background
        ctx.clearRect(0, 0, 150, 150);
        
        // Draw a circle background
        ctx.fillStyle = '#FF6B6B';
        ctx.beginPath();
        ctx.arc(75, 75, 60, 0, 2 * Math.PI);
        ctx.fill();
        
        // Draw text
        ctx.fillStyle = 'white';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('LOGO', 75, 85);
        
        function downloadLogo() {
            const link = document.createElement('a');
            link.download = 'test-logo.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
