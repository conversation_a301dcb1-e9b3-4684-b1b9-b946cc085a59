const axios = require('axios');

// Test the timeout fix with the same request that was failing
async function testTimeoutFix() {
  console.log('🧪 Testing Remotion timeout fix...\n');
  
  const testRequest = {
    clips: [
      "https://0x0.st/8n7Z.mp4",
      "https://0x0.st/8n7N.mp4", 
      "https://0x0.st/8n7q.mp4",
      "https://0x0.st/8nEJ.mp4",
      "https://0x0.st/8n7b.mp4",
      "https://0x0.st/8n7c.mp4",
      "https://0x0.st/8nRv.mp4",
      "https://0x0.st/8nUA.mp4"
    ],
    aspectRatio: "1:1",
    transition: "fade",
    subtitles: [
      {
        text: "Discover eco sip the reusable water bottle that",
        start: 0.35951087,
        end: 3.8747282,
        position: "bottom",
        backgroundColor: "linear-gradient(135deg, rgba(0,0,0,0.9), rgba(30,30,30,0.9))",
        textColor: "#ffffff",
        fontSize: 56,
        transition: "fade",
        transitionDuration: 0.5
      }
    ]
  };
  
  try {
    console.log('📤 Sending render request...');
    const response = await axios.post('http://localhost:5001/render', testRequest, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 5000 // 5 second timeout for the request itself
    });
    
    console.log('✅ Request accepted:', response.data);
    const jobId = response.data.id;
    
    console.log('\n📊 Monitoring job progress...');
    
    // Monitor the job progress
    let attempts = 0;
    const maxAttempts = 60; // 5 minutes max
    
    while (attempts < maxAttempts) {
      try {
        const statusResponse = await axios.get(`http://localhost:5001/status/${jobId}`, {
          timeout: 5000
        });
        
        const status = statusResponse.data;
        console.log(`⏱️  Attempt ${attempts + 1}: ${status.status} - ${status.progress}`);
        
        if (status.status === 'completed') {
          console.log('\n🎉 SUCCESS! Video rendering completed without timeout!');
          console.log('📊 Final status:', status);
          
          if (status.local_download_url) {
            console.log(`🔗 Download URL: ${status.local_download_url}`);
          }
          
          if (status.download_url) {
            console.log(`☁️  Remote URL: ${status.download_url}`);
          }
          
          return true;
        } else if (status.status === 'failed') {
          console.log('\n❌ FAILED! Video rendering failed:');
          console.log('Error:', status.error);
          console.log('Steps completed:', status.steps);
          
          // Check if it's still a timeout error
          if (status.error && status.error.includes('delayRender')) {
            console.log('\n🔍 ANALYSIS: Still getting timeout errors. The fix may need adjustment.');
            console.log('Possible causes:');
            console.log('- Video caching is not working properly');
            console.log('- Remotion timeout configuration is not applied');
            console.log('- Network issues preventing video download');
          } else {
            console.log('\n🔍 ANALYSIS: Different error than timeout - this is progress!');
          }
          
          return false;
        }
        
        // Wait 5 seconds before next check
        await new Promise(resolve => setTimeout(resolve, 5000));
        attempts++;
        
      } catch (statusError) {
        console.log(`⚠️  Status check error: ${statusError.message}`);
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
    
    console.log('\n⏰ Timeout waiting for job completion');
    return false;
    
  } catch (error) {
    console.log('\n❌ Request failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('🔍 Server is not running. Please start it with: npm run dev');
    }
    
    return false;
  }
}

// Test video proxy endpoint
async function testVideoProxy() {
  console.log('\n🔄 Testing video proxy...');
  
  try {
    const testUrl = 'https://0x0.st/8n7Z.mp4';
    const proxyUrl = `http://localhost:5001/proxy-video?url=${encodeURIComponent(testUrl)}`;
    
    console.log(`📥 Testing proxy for: ${testUrl}`);
    
    const response = await axios.head(proxyUrl, { timeout: 10000 });
    
    console.log('✅ Video proxy working!');
    console.log(`   Status: ${response.status}`);
    console.log(`   Content-Type: ${response.headers['content-type']}`);
    console.log(`   Content-Length: ${response.headers['content-length']}`);
    
    return true;
  } catch (error) {
    console.log('❌ Video proxy failed:', error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 REMOTION TIMEOUT FIX TEST SUITE\n');
  console.log('This will test the fixes for the delayRender timeout issue.\n');
  
  // Test 1: Video proxy
  const proxyWorking = await testVideoProxy();
  
  // Test 2: Full render with timeout fix
  const renderWorking = await testTimeoutFix();
  
  console.log('\n📊 TEST RESULTS:');
  console.log(`   🔄 Video Proxy: ${proxyWorking ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   🎬 Render Test: ${renderWorking ? '✅ PASS' : '❌ FAIL'}`);
  
  if (proxyWorking && renderWorking) {
    console.log('\n🎉 ALL TESTS PASSED! The timeout fix is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the logs above for details.');
  }
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testTimeoutFix, testVideoProxy };
