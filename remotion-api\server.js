const express = require("express");
const path = require("path");
const fs = require("fs");
const { v4: uuidv4 } = require("uuid");

// Import helper modules
const { renderVideo } = require("./srv/video/mergeVideos");
const { uploadTo0x0 } = require("./srv/utils/uploadTo0x0");
const { cleanupFiles } = require("./srv/utils/cleanupTemp");
const { logDebug } = require("./srv/utils/debugLog");
const { createVideoProxy } = require("./srv/utils/videoProxy");

const TEMP_OUT = path.join(__dirname, "temp");
if (!fs.existsSync(TEMP_OUT)) fs.mkdirSync(TEMP_OUT);

const RENDERS_OUT = path.join(__dirname, "renders");
if (!fs.existsSync(RENDERS_OUT)) fs.mkdirSync(RENDERS_OUT);

const app = express();
app.use(express.json());

// Serve rendered videos
app.use("/downloads", express.static(RENDERS_OUT));

// Serve public directory for cached videos (needed for Remotion staticFile)
const PUBLIC_DIR = path.join(__dirname, "public");
if (!fs.existsSync(PUBLIC_DIR)) fs.mkdirSync(PUBLIC_DIR, { recursive: true });
app.use("/public", express.static(PUBLIC_DIR));

// Add video proxy for remote video handling
app.use(createVideoProxy());

const jobStatus = {}; // In-memory job tracker

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "2.0.0",
    features: {
      remote_urls: "Primary support for https:// video URLs",
      local_files: "Testing support for local MP4 files in public/ directory",
      subtitles: "Text overlay with timing",
      audio: "Voice-over and background music support",
      output: "1920x1080 H264 MP4 videos"
    },
    endpoints: {
      render: "POST /render",
      status: "GET /status/:id",
      download: "GET /downloads/:id.mp4",
      health: "GET /health"
    }
  });
});

// Start render job
app.post("/render", async (req, res) => {
  const jobId = uuidv4();
  const {
    clips = [],
    clipDurations = [], // NEW: Array of durations for each clip
    voiceOver,
    voiceOverVolume = 1.0, // NEW: Voice-over volume control
    music,
    musicVolume = 0.3, // NEW: Background music volume control
    aspectRatio = 'auto',
    transition = 'fade',
    subtitles = [],
    overlays = []
  } = req.body;

  // Log the incoming request
  console.log(`\n🎬 NEW RENDER REQUEST - Job ID: ${jobId}`);
  console.log(`📊 Request details:`);
  console.log(`   🎞️  Clips: ${clips.length} clips`);
  clips.forEach((clip, i) => {
    const duration = clipDurations[i] ? `${clipDurations[i]}s` : 'auto-detect';
    console.log(`      ${i + 1}. ${clip} (${duration})`);
  });
  console.log(`   ⏱️  Clip Durations: ${clipDurations.length > 0 ? clipDurations.join('s, ') + 's' : 'auto-detect'}`);
  console.log(`   🎨 Aspect Ratio: ${aspectRatio}`);
  console.log(`   🔄 Transition: ${transition}`);
  console.log(`   📝 Subtitles: ${subtitles.length} subtitles`);
  if (subtitles.length > 0) {
    subtitles.forEach((sub, i) => {
      console.log(`      ${i + 1}. "${sub.text}" (${sub.start}s - ${sub.end}s)`);
    });
  }
  console.log(`   🖼️  Overlays: ${overlays.length} overlays`);
  if (voiceOver) console.log(`   🎤 Voice Over: ${voiceOver} (volume: ${voiceOverVolume})`);
  if (music) console.log(`   🎵 Music: ${music} (volume: ${musicVolume})`);

  // Allow empty clips if subtitles are provided
  // Clips can be remote URLs (primary) or local filenames (testing)
  if ((!clips || clips.length === 0) && (!subtitles || subtitles.length === 0)) {
    console.log(`❌ Request validation failed: No clips or subtitles provided`);
    return res.status(400).json({
      error: "At least one clip or subtitle is required",
      examples: {
        remote_urls: ["https://0x0.st/8kgm.mp4", "https://example.com/video.mp4"],
        local_files: ["video1.mp4", "video2.mp4"],
        subtitle_only: []
      }
    });
  }

  // Initialize job status
  jobStatus[jobId] = {
    id: jobId,
    status: "processing",
    progress: "Job created",
    createdAt: new Date().toISOString(),
    steps: ["Job created"]
  };

  console.log(`✅ Job ${jobId} initialized and queued for processing`);

  res.json({
    id: jobId,
    status: `/status/${jobId}`,
    message: "Render job started successfully"
  });

  // Process in background
  (async () => {
    const tempFiles = [];
    let outputPath = null;

    try {
      console.log(`\n🔄 STARTING BACKGROUND PROCESSING - Job ${jobId}`);

      jobStatus[jobId].progress = "Processing clips";
      jobStatus[jobId].steps.push("Processing clips");
      console.log(`📋 Step: Processing clips`);

      // Create output path in renders directory for permanent storage
      outputPath = path.join(RENDERS_OUT, `${jobId}.mp4`);
      console.log(`📁 Output path: ${outputPath}`);

      jobStatus[jobId].progress = "Remotion rendering started";
      jobStatus[jobId].steps.push("Remotion rendering started");
      console.log(`🎬 Step: Remotion rendering started`);

      // Render video using Remotion
      console.log(`🚀 Calling renderVideo function...`);
      const startTime = Date.now();

      await renderVideo({
        clips,
        clipDurations, // Pass the user-provided durations
        voiceOver,
        voiceOverVolume, // Pass voice-over volume
        music,
        musicVolume, // Pass music volume
        aspectRatio,
        transition,
        subtitles,
        overlays,
        tempDir: TEMP_OUT,
        tempFiles,
        outputPath
      });

      const renderTime = ((Date.now() - startTime) / 1000).toFixed(2);
      console.log(`✅ Video rendering completed in ${renderTime}s`);

      jobStatus[jobId].progress = "Video rendered successfully";
      jobStatus[jobId].steps.push("Video rendered successfully");

      // Provide local download URL
      const localDownloadUrl = `http://localhost:5001/downloads/${jobId}.mp4`;
      console.log(`🔗 Local download URL: ${localDownloadUrl}`);

      // Try to upload to 0x0.st (optional)
      let uploadUrl = null;
      try {
        jobStatus[jobId].progress = "Uploading video";
        jobStatus[jobId].steps.push("Uploading video");
        console.log(`☁️  Step: Uploading video to 0x0.st...`);

        uploadUrl = await uploadTo0x0(outputPath);
        console.log(`✅ Upload successful: ${uploadUrl}`);

        jobStatus[jobId].progress = "Upload completed";
        jobStatus[jobId].steps.push("Upload completed");
      } catch (uploadError) {
        console.warn(`⚠️  Upload failed: ${uploadError.message}`);
        jobStatus[jobId].steps.push("Upload failed - local download available");
      }

      // Mark as completed
      jobStatus[jobId].status = "completed";
      jobStatus[jobId].progress = "Complete";
      jobStatus[jobId].completedAt = new Date().toISOString();
      jobStatus[jobId].local_download_url = localDownloadUrl;
      if (uploadUrl) {
        jobStatus[jobId].download_url = uploadUrl;
      }

      console.log(`🎉 JOB COMPLETED SUCCESSFULLY - ${jobId}`);
      console.log(`   📁 Local file: ${outputPath}`);
      console.log(`   🔗 Local URL: ${localDownloadUrl}`);
      if (uploadUrl) {
        console.log(`   ☁️  Remote URL: ${uploadUrl}`);
      }

    } catch (error) {
      console.error(`❌ JOB FAILED - ${jobId}: ${error.message}`);
      console.error(`Stack trace:`, error.stack);

      logDebug(jobId, `Error: ${error.message}`, "error");
      jobStatus[jobId].status = "failed";
      jobStatus[jobId].error = error.message;
      jobStatus[jobId].failedAt = new Date().toISOString();
    } finally {
      // Cleanup only temp files, keep the rendered video
      try {
        console.log(`🧹 Cleaning up temporary files...`);
        if (tempFiles.length > 0) {
          await cleanupFiles(tempFiles);
          console.log(`✅ Cleaned up ${tempFiles.length} temporary files`);
        } else {
          console.log(`ℹ️  No temporary files to clean up`);
        }
        jobStatus[jobId].steps.push("Temporary files cleaned up");
      } catch (cleanupErr) {
        console.warn(`⚠️  Cleanup error: ${cleanupErr.message}`);
      }

      console.log(`🏁 Background processing finished for job ${jobId}\n`);
    }
  })();
});

// Get job status
app.get("/status/:id", (req, res) => {
  const { id } = req.params;
  const job = jobStatus[id];

  if (!job) {
    return res.status(404).json({ error: "Job not found" });
  }

  res.json(job);
});

const PORT = process.env.PORT || 5001;

// Add error handling
process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection:', reason);
});

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`\n🚀 REMOTION API SERVER STARTED SUCCESSFULLY`);
  console.log(`   🌐 Local: http://localhost:${PORT}`);
  console.log(`   🌐 Network: http://0.0.0.0:${PORT}`);
  console.log(`   📊 Status: GET /status/:id`);
  console.log(`   🎬 Render: POST /render`);
  console.log(`   ❤️  Health: GET /health`);
  console.log(`   ⏰ Started: ${new Date().toISOString()}`);
  console.log(`   📁 Temp dir: ${TEMP_OUT}`);
  console.log(`   📁 Renders dir: ${RENDERS_OUT}`);
  console.log(`\n✅ Server is ready to accept requests!\n`);
});

// Handle server errors
server.on('error', (err) => {
  console.error(`❌ Server error:`, err);
  if (err.code === 'EADDRINUSE') {
    console.error(`Port ${PORT} is already in use. Please use a different port or stop the existing server.`);
    process.exit(1);
  }
});

// Keep the server alive with better configuration
server.keepAliveTimeout = 65000; // 65 seconds
server.headersTimeout = 66000; // 66 seconds (slightly higher than keepAliveTimeout)

// Prevent the process from exiting
process.stdin.resume();

// Add periodic heartbeat to show server is alive
setInterval(() => {
  const activeJobs = Object.keys(jobStatus).filter(id => jobStatus[id].status === 'processing').length;
  if (activeJobs > 0) {
    console.log(`💓 Server heartbeat - ${activeJobs} active job(s) processing...`);
  }
}, 30000); // Every 30 seconds

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
