const axios = require('axios');

// Test the OffthreadVideo approach for stuttering fix
async function testOffthreadVideo() {
  console.log('🧪 Testing OFFTHREAD VIDEO approach for stutter fix...\n');
  
  // Simple test with 2 clips to isolate the stuttering issue
  const testRequest = {
    clips: [
      "https://0x0.st/8n7Z.mp4",
      "https://0x0.st/8n7N.mp4"
    ],
    clipDurations: [2.0, 2.5],
    voiceOver: "https://0x0.st/85oT.mp3",
    aspectRatio: "1:1",
    transition: "fade",
    subtitles: [
      {
        text: "Testing OffthreadVideo - should be smooth now!",
        start: 0.5,
        end: 3.5,
        position: "bottom",
        backgroundColor: "rgba(0,0,0,0.8)",
        textColor: "#ffffff",
        fontSize: 48
      }
    ]
  };
  
  try {
    console.log('📤 Sending render request with OffthreadVideo component...');
    console.log('   🎞️  Using OffthreadVideo instead of Video component');
    console.log('   🚀 Better performance and smoother playback expected');
    console.log('   🎤 Voice-over included for full test');
    console.log('   📝 Subtitles included for timing reference');
    console.log('');
    
    const response = await axios.post('http://localhost:5001/render', testRequest, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 5000
    });
    
    console.log('✅ Request accepted:', response.data);
    const jobId = response.data.id;
    
    console.log('\n📊 Monitoring job progress...');
    console.log('🔍 Watch for these improvements in the final video:');
    console.log('   • No pause-continue stuttering');
    console.log('   • Smooth frame-by-frame playback');
    console.log('   • Perfect audio-video sync');
    console.log('   • Consistent playback speed');
    console.log('');
    
    let attempts = 0;
    const maxAttempts = 30;
    
    while (attempts < maxAttempts) {
      try {
        const statusResponse = await axios.get(`http://localhost:5001/status/${jobId}`, {
          timeout: 5000
        });
        
        const status = statusResponse.data;
        console.log(`⏱️  Attempt ${attempts + 1}: ${status.status} - ${status.progress}`);
        
        if (status.status === 'completed') {
          console.log('\n🎉 SUCCESS! OffthreadVideo rendering completed!');
          console.log('📊 Final status:', status);
          
          if (status.local_download_url) {
            console.log(`🔗 Download URL: ${status.local_download_url}`);
            console.log('');
            console.log('🎬 PLEASE TEST THE VIDEO:');
            console.log('   1. Download and play the video');
            console.log('   2. Check if stuttering is eliminated');
            console.log('   3. Verify smooth playback throughout');
            console.log('   4. Confirm audio-video sync is perfect');
            console.log('');
            console.log('✅ If smooth: OffthreadVideo fixed the issue!');
            console.log('❌ If still stuttering: We need a different approach');
          }
          
          return true;
        } else if (status.status === 'failed') {
          console.log('\n❌ FAILED! Error details:');
          console.log('Error:', status.error);
          
          if (status.error && status.error.includes('OffthreadVideo')) {
            console.log('\n🔍 OffthreadVideo might not be available or compatible.');
            console.log('We may need to try a different approach.');
          }
          
          return false;
        }
        
        await new Promise(resolve => setTimeout(resolve, 5000));
        attempts++;
        
      } catch (statusError) {
        console.log(`⚠️  Status check error: ${statusError.message}`);
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
    
    console.log('\n⏰ Timeout waiting for job completion');
    return false;
    
  } catch (error) {
    console.log('\n❌ Request failed:', error.message);
    return false;
  }
}

// Show alternative approaches if OffthreadVideo doesn't work
function showAlternativeApproaches() {
  console.log('\n🔧 ALTERNATIVE APPROACHES IF STUTTERING PERSISTS\n');
  
  console.log('🎯 APPROACH 1: Reduce Clip Durations');
  console.log('   • Use shorter durations (1.0-1.5s per clip)');
  console.log('   • Reduces chance of video extending beyond actual content');
  console.log('   • Example: clipDurations: [1.0, 1.2, 1.1, 1.3]');
  console.log('');
  
  console.log('🎯 APPROACH 2: Pre-process Videos');
  console.log('   • Convert all videos to same format/frame rate');
  console.log('   • Use consistent resolution and codec');
  console.log('   • Eliminate format mismatches');
  console.log('');
  
  console.log('🎯 APPROACH 3: Local Video Testing');
  console.log('   • Download videos locally first');
  console.log('   • Test with local files to isolate network issues');
  console.log('   • Compare local vs remote performance');
  console.log('');
  
  console.log('🎯 APPROACH 4: Frame Rate Matching');
  console.log('   • Ensure all videos have same frame rate (30fps)');
  console.log('   • Use video editing tools to standardize');
  console.log('   • Eliminates frame rate conversion stuttering');
  console.log('');
  
  console.log('🎯 APPROACH 5: Different Video Sources');
  console.log('   • Try videos from different sources');
  console.log('   • Test with higher quality/more stable URLs');
  console.log('   • Some video hosts have better streaming performance');
}

// Main test function
async function runTest() {
  console.log('🚀 OFFTHREAD VIDEO STUTTER FIX TEST\n');
  console.log('Testing OffthreadVideo component as a solution for video stuttering.\n');
  
  // Test the OffthreadVideo approach
  const offthreadTest = await testOffthreadVideo();
  
  // Show alternatives
  showAlternativeApproaches();
  
  console.log('\n📊 TEST RESULTS:');
  console.log(`   🎬 OffthreadVideo Test: ${offthreadTest ? '✅ COMPLETED' : '❌ FAILED'}`);
  
  if (offthreadTest) {
    console.log('\n🎯 NEXT STEPS:');
    console.log('1. Download and test the generated video');
    console.log('2. Check if stuttering is eliminated');
    console.log('3. If smooth: OffthreadVideo is the solution!');
    console.log('4. If still stuttering: Try the alternative approaches above');
  } else {
    console.log('\n⚠️  OffthreadVideo test failed. Consider the alternative approaches above.');
  }
}

// Run the test
if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = { testOffthreadVideo };
