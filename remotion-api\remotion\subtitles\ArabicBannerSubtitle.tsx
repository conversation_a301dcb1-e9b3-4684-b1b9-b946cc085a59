import React from "react";

/**
 * ArabicBannerSubtitle
 * - Two lines, both bold, uppercase (if not Arabic), centered
 * - Top line: backgroundColor (default: black), white text, rounded pill
 * - Bottom line: white background, colored text (backgroundColor or orange), rounded pill
 * - RTL support for Arabic
 * - backgroundColor: required for top, used for bottom text color if present
 */
export const ArabicBannerSubtitle: React.FC<{
  text: string;
  backgroundColor?: string;
  position?: "bottom" | "top" | "center";
  style?: React.CSSProperties;
  animationFrame?: number;
}> = ({
  text,
  backgroundColor,
  position = "bottom",
  style = {},
  animationFrame = 0,
}) => {
  // Split text into two lines
  const [top, bottom] = text.split("\n").length > 1
    ? text.split("\n")
    : [text, ""];

  // Detect Arabic
  const isArabic = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/.test(text);

  // Animation: pop-in
  const anim = {
    transform: `scale(${0.8 + 0.2 * Math.min(animationFrame / 10, 1)})`,
    opacity: Math.min(animationFrame / 10, 1),
    transition: "all 0.4s cubic-bezier(.5,1.8,.5,1)",
  };

  // Default color logic
  const mainColor = backgroundColor || "#000";
  const bottomTextColor = backgroundColor || "#ff8800";

  const containerStyle: React.CSSProperties = {
    position: "absolute",
    left: 0,
    right: 0,
    [position]: 120,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    pointerEvents: "none",
    gap: 12,
    ...style,
    ...anim,
  };

  const fontFamily = isArabic
    ? 'Arial, "Noto Sans Arabic", "Amiri", "Scheherazade", sans-serif'
    : 'Impact, Arial Black, sans-serif';
  const fontWeight = 900;
  const fontSize = 54;

  return (
    <div style={containerStyle}>
      <span
        style={{
          fontFamily,
          fontWeight,
          fontSize,
          color: "#fff",
          background: mainColor,
          borderRadius: 32,
          padding: "10px 32px",
          marginBottom: 6,
          textAlign: "center",
          direction: isArabic ? "rtl" : "ltr",
          textTransform: isArabic ? undefined : "uppercase",
          boxShadow: "0 2px 12px rgba(0,0,0,0.18)",
          letterSpacing: 1.2,
        }}
      >
        {top}
      </span>
      {bottom && (
        <span
          style={{
            fontFamily,
            fontWeight,
            fontSize,
            color: bottomTextColor,
            background: "#fff",
            borderRadius: 32,
            padding: "10px 32px",
            textAlign: "center",
            direction: isArabic ? "rtl" : "ltr",
            textTransform: isArabic ? undefined : "uppercase",
            boxShadow: "0 2px 12px rgba(0,0,0,0.18)",
            letterSpacing: 1.2,
          }}
        >
          {bottom}
        </span>
      )}
    </div>
  );
};

export const ArabicBannerSubtitleMeta = {
  name: "arabic_banner",
  displayName: "Arabic Banner",
  defaultBackground: "#000",
  allowsBackground: true,
  defaultPosition: "bottom",
  animated: true,
};
