const axios = require('axios');

// Test the new volume controls
async function testVolumeControls() {
  console.log('🧪 Testing AUDIO VOLUME CONTROLS...\n');
  
  // Test with custom volume levels
  const testRequest = {
    clips: [
      "https://0x0.st/8n7Z.mp4",
      "https://0x0.st/8n7N.mp4"
    ],
    clipDurations: [2.0, 2.5],
    voiceOver: "https://0x0.st/85oT.mp3",
    voiceOverVolume: 1.2, // Boost voice-over volume
    music: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
    musicVolume: 0.15, // Quiet background music
    aspectRatio: "1:1",
    transition: "fade",
    subtitles: [
      {
        text: "Testing custom audio volumes!",
        start: 0.5,
        end: 3.5,
        position: "bottom",
        backgroundColor: "rgba(0,0,0,0.8)",
        textColor: "#ffffff",
        fontSize: 48
      }
    ]
  };
  
  try {
    console.log('📤 Sending render request with custom volume controls...');
    console.log('   🎤 Voice-over volume: 1.2 (20% boost)');
    console.log('   🎵 Music volume: 0.15 (quiet background)');
    console.log('   🎚️  Perfect audio balance expected');
    console.log('');
    
    const response = await axios.post('http://localhost:5001/render', testRequest, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 5000
    });
    
    console.log('✅ Request accepted:', response.data);
    const jobId = response.data.id;
    
    console.log('\n📊 Monitoring job progress (watching for volume settings)...');
    
    let attempts = 0;
    const maxAttempts = 30;
    
    while (attempts < maxAttempts) {
      try {
        const statusResponse = await axios.get(`http://localhost:5001/status/${jobId}`, {
          timeout: 5000
        });
        
        const status = statusResponse.data;
        console.log(`⏱️  Attempt ${attempts + 1}: ${status.status} - ${status.progress}`);
        
        if (status.status === 'completed') {
          console.log('\n🎉 SUCCESS! Volume controls working!');
          console.log('📊 Final status:', status);
          
          if (status.local_download_url) {
            console.log(`🔗 Download URL: ${status.local_download_url}`);
            console.log('');
            console.log('🎧 AUDIO TEST RESULTS:');
            console.log('   🎤 Voice-over should be 20% louder than normal');
            console.log('   🎵 Background music should be very quiet');
            console.log('   🎚️  Perfect balance between voice and music');
            console.log('   📢 Clear, professional audio mix');
          }
          
          return true;
        } else if (status.status === 'failed') {
          console.log('\n❌ FAILED! Error details:');
          console.log('Error:', status.error);
          return false;
        }
        
        await new Promise(resolve => setTimeout(resolve, 5000));
        attempts++;
        
      } catch (statusError) {
        console.log(`⚠️  Status check error: ${statusError.message}`);
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
    
    console.log('\n⏰ Timeout waiting for job completion');
    return false;
    
  } catch (error) {
    console.log('\n❌ Request failed:', error.message);
    return false;
  }
}

// Test extreme volume settings
async function testExtremeVolumes() {
  console.log('\n🧪 Testing EXTREME VOLUME SETTINGS...\n');
  
  const testRequest = {
    clips: [
      "https://0x0.st/8n7Z.mp4"
    ],
    clipDurations: [2.0],
    voiceOver: "https://0x0.st/85oT.mp3",
    voiceOverVolume: 2.0, // Maximum volume
    music: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
    musicVolume: 0.05, // Very quiet
    aspectRatio: "1:1",
    subtitles: [
      {
        text: "Extreme volume test",
        start: 0.5,
        end: 1.5,
        position: "bottom"
      }
    ]
  };
  
  try {
    console.log('📤 Testing extreme volume settings...');
    console.log('   🎤 Voice-over: 2.0 (maximum volume)');
    console.log('   🎵 Music: 0.05 (barely audible)');
    
    const response = await axios.post('http://localhost:5001/render', testRequest, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 5000
    });
    
    console.log('✅ Extreme volume test accepted:', response.data);
    return true;
    
  } catch (error) {
    console.log('❌ Extreme volume test failed:', error.message);
    return false;
  }
}

// Show volume control explanation
function showVolumeControls() {
  console.log('\n🎚️  AUDIO VOLUME CONTROLS EXPLANATION\n');
  
  console.log('🎤 VOICE-OVER VOLUME (voiceOverVolume):');
  console.log('   • Range: 0.0 to 2.0');
  console.log('   • Default: 1.0 (normal volume)');
  console.log('   • 0.0 = Silent');
  console.log('   • 0.5 = Half volume');
  console.log('   • 1.0 = Normal volume');
  console.log('   • 1.5 = 50% boost');
  console.log('   • 2.0 = Double volume (maximum)');
  console.log('');
  
  console.log('🎵 BACKGROUND MUSIC VOLUME (musicVolume):');
  console.log('   • Range: 0.0 to 2.0');
  console.log('   • Default: 0.3 (quiet background)');
  console.log('   • 0.0 = Silent');
  console.log('   • 0.1 = Very quiet');
  console.log('   • 0.3 = Quiet background (default)');
  console.log('   • 0.5 = Moderate background');
  console.log('   • 1.0 = Equal to voice-over');
  console.log('   • 2.0 = Loud background (not recommended)');
  console.log('');
  
  console.log('🎯 RECOMMENDED COMBINATIONS:');
  console.log('   📢 Clear Narration: voiceOver: 1.2, music: 0.2');
  console.log('   🎬 Cinematic: voiceOver: 1.0, music: 0.4');
  console.log('   🎙️  Podcast Style: voiceOver: 1.3, music: 0.1');
  console.log('   🎵 Music Focus: voiceOver: 0.8, music: 0.6');
  console.log('   🔇 Voice Only: voiceOver: 1.0, music: 0.0');
  console.log('');
  
  console.log('📝 EXAMPLE REQUEST:');
  console.log('```json');
  console.log('{');
  console.log('  "clips": ["video1.mp4", "video2.mp4"],');
  console.log('  "clipDurations": [2.0, 2.5],');
  console.log('  "voiceOver": "https://example.com/narration.mp3",');
  console.log('  "voiceOverVolume": 1.2,  // 20% boost');
  console.log('  "music": "https://example.com/background.mp3",');
  console.log('  "musicVolume": 0.2,      // Quiet background');
  console.log('  "aspectRatio": "1:1"');
  console.log('}');
  console.log('```');
}

// Main test function
async function runTests() {
  console.log('🚀 AUDIO VOLUME CONTROLS TEST SUITE\n');
  console.log('Testing the new separate volume controls for voice-over and music.\n');
  
  // Show the volume controls explanation
  showVolumeControls();
  
  // Test 1: Custom volume levels
  const volumeTest = await testVolumeControls();
  
  // Test 2: Extreme volumes
  const extremeTest = await testExtremeVolumes();
  
  console.log('\n📊 TEST RESULTS:');
  console.log(`   🎚️  Volume Controls Test: ${volumeTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   🔊 Extreme Volume Test: ${extremeTest ? '✅ PASS' : '❌ FAIL'}`);
  
  if (volumeTest) {
    console.log('\n🎉 VOLUME CONTROLS WORKING PERFECTLY!');
    console.log('You now have complete control over your audio mix.');
    console.log('');
    console.log('🎯 WHAT YOU CAN DO:');
    console.log('   ✅ Boost voice-over for clarity');
    console.log('   ✅ Reduce background music to perfect level');
    console.log('   ✅ Create professional audio balance');
    console.log('   ✅ Fine-tune volumes for any content type');
    console.log('   ✅ Silent audio tracks when needed');
  } else {
    console.log('\n⚠️  Volume controls test failed. Check server logs for details.');
  }
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testVolumeControls, testExtremeVolumes };
