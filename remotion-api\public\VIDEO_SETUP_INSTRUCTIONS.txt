
# Test Video Setup

To test your video merger API, you need to add MP4 video files to this directory.

## Required Files:
- 8klQ.mp4
- 8klP(1).mp4

## Options to get test videos:

### Option 1: Use sample videos from the internet
You can download sample videos from:
- https://sample-videos.com/
- https://file-examples.com/index.php/sample-video-files/
- https://www.learningcontainer.com/mp4-sample-video-files-download/

### Option 2: Create simple test videos with FFmpeg (if available)
ffmpeg -f lavfi -i testsrc=duration=5:size=1920x1080:rate=30 -c:v libx264 public/8klQ.mp4
ffmpeg -f lavfi -i testsrc2=duration=5:size=1920x1080:rate=30 -c:v libx264 public/8klP(1).mp4

### Option 3: Use any MP4 files you have
Just rename any MP4 files to:
- 8klQ.mp4
- 8klP(1).mp4

## Important Requirements:
- Files must be MP4 format
- Dimensions should be even numbers (divisible by 2)
- H264 codec recommended
- Place files directly in the public/ directory

## Test Command:
Once you have the files, test with:
curl -X POST http://localhost:5001/render \
  -H "Content-Type: application/json" \
  -d '{"clips": ["8klQ.mp4", "8klP(1).mp4"], "subtitles": [{"text": "Hello!", "start": 0, "end": 3}]}'
