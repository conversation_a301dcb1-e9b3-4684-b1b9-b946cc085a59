import { Composition } from "remotion";
import { HelloWorld, helloWorldCompSchema } from "./HelloWorld";
import { VideoMerger, videoMergerSchema } from "./VideoMerger";

// Import test subtitle composition
import { TestTikTokTrendingSubtitle } from "./subtitles/TestTikTokTrendingSubtitle";
import { TestArabicBannerSubtitle } from "./subtitles/TestArabicBannerSubtitle";

// Each <Composition> is an entry in the sidebar!

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        // You can take the "id" to render a video:
        // npx remotion render src/index.ts <id> out/video.mp4
        id="HelloWorld"
        component={HelloWorld}
        durationInFrames={800}
        fps={30}
        width={1920}
        height={1080}
        // You can override these props for each render:
        // https://www.remotion.dev/docs/parametrized-rendering
        schema={helloWorldCompSchema}
        defaultProps={{
          titleText: "Render Server Template",
          titleColor: "#000000",
          logoColor1: "#91EAE4",
          logoColor2: "#86A8E7",
        }}
      />
      <Composition
        id="VideoMerger"
        component={VideoMerger}
        durationInFrames={480} // 16 seconds at 30fps - matches your subtitle timing
        fps={30}
        width={1920}
        height={1080}
        schema={videoMergerSchema}
        defaultProps={{
          clips: ["https://0x0.st/8kgm.mp4"], // Example remote URL
          clipDurations: [10], // Example duration
          voiceOver: null,
          music: null,
          aspectRatio: "16:9",
          transition: "fade",
          subtitles: [
            {
              text: "🌟 Enhanced Subtitles!",
              start: 0,
              end: 3,
              backgroundColor: "rainbow",
              textColor: "white",
              position: "top",
              transition: "bounce",
              transitionDuration: 0.8
            },
            {
              text: "Custom gradient background",
              start: 4,
              end: 7,
              backgroundColor: "linear-gradient(135deg, #667eea, #764ba2)",
              textColor: "#ffffff",
              position: "center",
              transition: "slide-up"
            }
          ],
          overlays: [
            {
              src: "https://via.placeholder.com/150x150/FF6B6B/FFFFFF?text=LOGO",
              start: 0,
              end: 10,
              position: "top-right",
              width: 120,
              opacity: 0.9,
              animation: "fade"
            }
          ],
        }}
      />
      <Composition
        id="TestTikTokTrendingSubtitle"
        component={TestTikTokTrendingSubtitle}
        durationInFrames={120}
        fps={30}
        width={1920}
        height={1080}
        defaultProps={{}}
      />
      <Composition
        id="TestArabicBannerSubtitle"
        component={TestArabicBannerSubtitle}
        durationInFrames={120}
        fps={30}
        width={1920}
        height={1080}
        defaultProps={{}}
      />
    </>
  );
};
