const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');
const { v4: uuidv4 } = require('uuid');

/**
 * Downloads a remote video to local cache for reliable Remotion access
 * @param {string} videoUrl - Remote video URL
 * @param {string} cacheDir - Directory to cache videos
 * @returns {Promise<string>} - Path to cached video file
 */
async function cacheRemoteVideo(videoUrl, cacheDir) {
  console.log(`📥 Caching remote video: ${videoUrl}`);
  
  // Create cache directory if it doesn't exist
  if (!fs.existsSync(cacheDir)) {
    fs.mkdirSync(cacheDir, { recursive: true });
  }
  
  // Generate a unique filename for the cached video
  const videoId = uuidv4();
  const extension = path.extname(videoUrl.split('?')[0]) || '.mp4';
  const cachedPath = path.join(cacheDir, `${videoId}${extension}`);
  
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const timeout = 25000; // 25 second timeout (less than Remotion's 28s)
    
    // Choose http or https based on URL
    const client = videoUrl.startsWith('https:') ? https : http;
    
    const request = client.get(videoUrl, {
      timeout: timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    }, (response) => {
      // Handle redirects
      if (response.statusCode >= 300 && response.statusCode < 400 && response.headers.location) {
        console.log(`🔄 Following redirect to: ${response.headers.location}`);
        return cacheRemoteVideo(response.headers.location, cacheDir)
          .then(resolve)
          .catch(reject);
      }
      
      if (response.statusCode !== 200) {
        return reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
      }
      
      const fileStream = fs.createWriteStream(cachedPath);
      let downloadedBytes = 0;
      const totalBytes = parseInt(response.headers['content-length']) || 0;
      
      response.on('data', (chunk) => {
        downloadedBytes += chunk.length;
        if (totalBytes > 0) {
          const progress = ((downloadedBytes / totalBytes) * 100).toFixed(1);
          if (downloadedBytes % (1024 * 1024) < chunk.length) { // Log every MB
            console.log(`📊 Download progress: ${progress}% (${(downloadedBytes / 1024 / 1024).toFixed(1)}MB)`);
          }
        }
      });
      
      response.pipe(fileStream);
      
      fileStream.on('finish', () => {
        const downloadTime = ((Date.now() - startTime) / 1000).toFixed(2);
        const fileSizeMB = (fs.statSync(cachedPath).size / 1024 / 1024).toFixed(2);
        console.log(`✅ Video cached successfully in ${downloadTime}s (${fileSizeMB}MB): ${cachedPath}`);
        resolve(cachedPath);
      });
      
      fileStream.on('error', (error) => {
        console.error(`❌ File write error: ${error.message}`);
        // Clean up partial file
        if (fs.existsSync(cachedPath)) {
          fs.unlinkSync(cachedPath);
        }
        reject(error);
      });
    });
    
    request.on('timeout', () => {
      request.destroy();
      console.error(`⏰ Download timeout for ${videoUrl}`);
      reject(new Error(`Download timeout after ${timeout}ms`));
    });
    
    request.on('error', (error) => {
      console.error(`❌ Download error for ${videoUrl}: ${error.message}`);
      reject(error);
    });
    
    // Set overall timeout
    setTimeout(() => {
      if (!request.destroyed) {
        request.destroy();
        reject(new Error(`Overall timeout after ${timeout}ms`));
      }
    }, timeout);
  });
}

/**
 * Caches multiple videos concurrently with error handling
 * @param {Array<string>} videoUrls - Array of video URLs
 * @param {string} cacheDir - Cache directory
 * @returns {Promise<Array<{url: string, cachedPath: string, error?: string}>>}
 */
async function cacheMultipleVideos(videoUrls, cacheDir) {
  console.log(`📦 Caching ${videoUrls.length} videos...`);
  
  const results = await Promise.allSettled(
    videoUrls.map(async (url, index) => {
      try {
        console.log(`📥 Starting download ${index + 1}/${videoUrls.length}: ${url}`);
        const cachedPath = await cacheRemoteVideo(url, cacheDir);
        return { url, cachedPath };
      } catch (error) {
        console.error(`❌ Failed to cache video ${index + 1}: ${error.message}`);
        return { url, error: error.message };
      }
    })
  );
  
  const successful = results.filter(r => r.status === 'fulfilled' && !r.value.error).length;
  const failed = results.length - successful;
  
  console.log(`📊 Caching complete: ${successful} successful, ${failed} failed`);
  
  return results.map(r => r.status === 'fulfilled' ? r.value : { url: 'unknown', error: r.reason.message });
}

/**
 * Cleans up cached video files
 * @param {Array<string>} cachedPaths - Array of cached file paths to clean up
 */
function cleanupCachedVideos(cachedPaths) {
  console.log(`🧹 Cleaning up ${cachedPaths.length} cached videos...`);
  
  let cleaned = 0;
  cachedPaths.forEach(cachedPath => {
    try {
      if (fs.existsSync(cachedPath)) {
        fs.unlinkSync(cachedPath);
        cleaned++;
      }
    } catch (error) {
      console.warn(`⚠️ Failed to clean up ${cachedPath}: ${error.message}`);
    }
  });
  
  console.log(`✅ Cleaned up ${cleaned} cached video files`);
}

module.exports = {
  cacheRemoteVideo,
  cacheMultipleVideos,
  cleanupCachedVideos
};
