{"name": "remotion-api", "version": "1.0.0", "description": "My Remotion video", "repository": {}, "license": "UNLICENSED", "private": true, "dependencies": {"@remotion/bundler": "4.0.325", "@remotion/cli": "4.0.325", "@remotion/renderer": "4.0.325", "@remotion/zod-types": "4.0.325", "express": "5.1.0", "react": "19.0.0", "react-dom": "19.0.0", "remotion": "4.0.325", "uuid": "^10.0.0", "zod": "3.22.3"}, "devDependencies": {"@remotion/eslint-config-flat": "4.0.325", "@remotion/eslint-plugin": "4.0.325", "@types/express": "5.0.1", "@types/react": "19.0.0", "@types/web": "0.0.166", "eslint": "9.19.0", "prettier": "3.3.3", "tsx": "4.19.3", "typescript": "5.8.2"}, "scripts": {"build": "remotion bundle", "dev": "tsx watch server", "start": "tsx server", "lint": "eslint . && tsc", "remotion:upgrade": "remotion upgrade", "remotion:studio": "remotion studio"}}