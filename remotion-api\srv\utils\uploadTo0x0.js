const { exec } = require("child_process");

/**
 * Upload a file to 0x0.st and get back the URL
 * @param {string} filePath
 * @returns {Promise<string>}
 */
function uploadTo0x0(filePath) {
  return new Promise((resolve, reject) => {
    const cmd = `curl -s -F "file=@${filePath}" https://0x0.st`;
    exec(cmd, (error, stdout, stderr) => {
      if (error || stderr) {
        return reject(new Error(stderr || error.message));
      }
      const url = stdout.trim();
      if (!url.startsWith("http")) {
        return reject(new Error("Invalid upload URL: " + url));
      }
      resolve(url);
    });
  });
}

module.exports = { uploadTo0x0 };
