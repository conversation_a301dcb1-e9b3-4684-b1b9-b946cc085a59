
This is a placeholder for test video files.
To properly test the video merger, you need to:

1. Add real MP4 video files to the public directory:
   - 8klQ.mp4
   - 8klP(1).mp4

2. Ensure the video files have:
   - Even-numbered dimensions (width and height divisible by 2)
   - H264 codec compatibility
   - Standard frame rates (24, 25, 30, or 60 fps)

The VideoMerger composition is now properly configured and the "width of NaNpx" error has been fixed!
