@echo off
echo 🖼️ Testing Image Overlay Features
echo ================================

echo.
echo 1. Logo Overlay Test (Top-Right)
echo --------------------------------
curl -X POST http://localhost:5001/render ^
  -H "Content-Type: application/json" ^
  -d "{\"clips\": [\"https://0x0.st/8kgm.mp4\"], \"overlays\": [{\"src\": \"https://via.placeholder.com/150x150/FF6B6B/FFFFFF?text=LOGO\", \"start\": 0, \"end\": 5, \"position\": \"top-right\", \"width\": 120, \"animation\": \"fade\"}]}"

echo.
echo 2. Call-to-Action Button (Bottom-Center with Bounce)
echo ---------------------------------------------------
curl -X POST http://localhost:5001/render ^
  -H "Content-Type: application/json" ^
  -d "{\"clips\": [\"https://0x0.st/8kgm.mp4\"], \"overlays\": [{\"src\": \"https://via.placeholder.com/300x80/4ECDC4/FFFFFF?text=SUBSCRIBE\", \"start\": 3, \"end\": 8, \"position\": \"bottom-center\", \"width\": 250, \"animation\": \"bounce\", \"animationDuration\": 1.2}]}"

echo.
echo 3. Multiple Overlays (Logo + Watermark + CTA)
echo ---------------------------------------------
curl -X POST http://localhost:5001/render ^
  -H "Content-Type: application/json" ^
  -d "{\"clips\": [\"https://0x0.st/8kgm.mp4\"], \"overlays\": [{\"src\": \"https://via.placeholder.com/100x100/667EEA/FFFFFF?text=LOGO\", \"start\": 0, \"end\": 8, \"position\": \"top-left\", \"width\": 80, \"animation\": \"fade\"}, {\"src\": \"https://via.placeholder.com/80x80/764BA2/FFFFFF?text=©\", \"start\": 0, \"end\": 8, \"position\": \"bottom-right\", \"width\": 60, \"opacity\": 0.6, \"animation\": \"none\"}, {\"src\": \"https://via.placeholder.com/200x60/FF6B6B/FFFFFF?text=CLICK+HERE\", \"start\": 5, \"end\": 8, \"position\": \"center\", \"width\": 180, \"animation\": \"zoom-in\"}]}"

echo.
echo 4. Animated Logo with Pulse Effect
echo ----------------------------------
curl -X POST http://localhost:5001/render ^
  -H "Content-Type: application/json" ^
  -d "{\"clips\": [\"https://0x0.st/8kgm.mp4\"], \"overlays\": [{\"src\": \"https://via.placeholder.com/120x120/F39C12/FFFFFF?text=PULSE\", \"start\": 0, \"end\": 6, \"position\": \"top-center\", \"width\": 100, \"animation\": \"pulse\"}]}"

echo.
echo 5. Rotating Watermark
echo ---------------------
curl -X POST http://localhost:5001/render ^
  -H "Content-Type: application/json" ^
  -d "{\"clips\": [\"https://0x0.st/8kgm.mp4\"], \"overlays\": [{\"src\": \"https://via.placeholder.com/80x80/E74C3C/FFFFFF?text=©\", \"start\": 0, \"end\": 8, \"position\": \"center-right\", \"width\": 60, \"opacity\": 0.7, \"animation\": \"rotate\"}]}"

echo.
echo 6. Subtitle + Logo Combination
echo ------------------------------
curl -X POST http://localhost:5001/render ^
  -H "Content-Type: application/json" ^
  -d "{\"clips\": [\"https://0x0.st/8kgm.mp4\"], \"subtitles\": [{\"text\": \"🌈 Amazing Video!\", \"start\": 0, \"end\": 4, \"backgroundColor\": \"rainbow\", \"position\": \"bottom\", \"transition\": \"bounce\"}], \"overlays\": [{\"src\": \"https://via.placeholder.com/100x100/2ECC71/FFFFFF?text=BRAND\", \"start\": 0, \"end\": 8, \"position\": \"top-right\", \"width\": 90, \"animation\": \"slide-in\"}]}"

echo.
echo 📝 Available Overlay Positions:
echo top-left, top-center, top-right
echo center-left, center, center-right
echo bottom-left, bottom-center, bottom-right
echo.
echo 🎭 Available Animations:
echo none, fade, slide-in, zoom-in, bounce, pulse, rotate
echo.
echo 💡 Pro Tips:
echo - Use PNG images with transparency for best results
echo - Logos work great in corners (top-right, top-left)
echo - Call-to-action buttons are effective in bottom-center
echo - Watermarks should use low opacity (0.3-0.7)
echo - Combine with subtitles for maximum impact!
