const axios = require('axios');

// Test the improved duration detection
async function testDurationDetection() {
  console.log('🧪 Testing improved duration detection...\n');
  
  // Test with 3 clips to see different duration estimates
  const testRequest = {
    clips: [
      "https://0x0.st/8n7Z.mp4",  // Small file
      "https://0x0.st/8nUA.mp4",  // Large file (1.10MB from logs)
      "https://0x0.st/8n7N.mp4"   // Medium file
    ],
    aspectRatio: "1:1",
    transition: "fade",
    subtitles: [
      {
        text: "Testing duration detection improvements",
        start: 0.5,
        end: 4.0,
        position: "bottom",
        backgroundColor: "rgba(0,0,0,0.8)",
        textColor: "#ffffff",
        fontSize: 48
      }
    ]
  };
  
  try {
    console.log('📤 Sending render request...');
    const response = await axios.post('http://localhost:5001/render', testRequest, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 5000
    });
    
    console.log('✅ Request accepted:', response.data);
    const jobId = response.data.id;
    
    console.log('\n📊 Monitoring job progress (watching for duration detection)...');
    
    let attempts = 0;
    const maxAttempts = 25; // ~2 minutes
    let durationDetectionSeen = false;
    
    while (attempts < maxAttempts) {
      try {
        const statusResponse = await axios.get(`http://localhost:5001/status/${jobId}`, {
          timeout: 5000
        });
        
        const status = statusResponse.data;
        console.log(`⏱️  Attempt ${attempts + 1}: ${status.status} - ${status.progress}`);
        
        // Check if we're in the duration detection phase
        if (status.progress && status.progress.includes('duration')) {
          durationDetectionSeen = true;
          console.log('🔍 Duration detection phase detected - check server logs for improvements');
        }
        
        if (status.status === 'completed') {
          console.log('\n🎉 SUCCESS! Video completed with improved duration detection!');
          console.log('📊 Final status:', status);
          
          if (status.local_download_url) {
            console.log(`🔗 Download URL: ${status.local_download_url}`);
          }
          
          return true;
        } else if (status.status === 'failed') {
          console.log('\n❌ FAILED! Error details:');
          console.log('Error:', status.error);
          return false;
        }
        
        // Wait 5 seconds before next check
        await new Promise(resolve => setTimeout(resolve, 5000));
        attempts++;
        
      } catch (statusError) {
        console.log(`⚠️  Status check error: ${statusError.message}`);
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
    
    console.log('\n⏰ Timeout waiting for job completion');
    return false;
    
  } catch (error) {
    console.log('\n❌ Request failed:', error.message);
    return false;
  }
}

// Analyze server logs for duration improvements
function analyzeDurationLogs() {
  console.log('\n📋 DURATION DETECTION ANALYSIS');
  console.log('Check your server logs for these improvements:');
  console.log('');
  console.log('✅ GOOD SIGNS:');
  console.log('   📊 "File-size estimated duration: X.Xs" - JS estimation working');
  console.log('   📏 Different duration values for different clips');
  console.log('   🎯 More accurate total duration calculation');
  console.log('');
  console.log('❌ STILL NEEDS WORK:');
  console.log('   📏 All clips showing same 2.5s duration');
  console.log('   ⚠️  "Using generic fallback" messages');
  console.log('');
  console.log('🔧 FOR BEST RESULTS:');
  console.log('   Install FFmpeg: winget install Gyan.FFmpeg');
  console.log('   Then restart server for exact durations');
}

// Test file size estimation directly
async function testFileSizeEstimation() {
  console.log('\n🧪 Testing file size estimation logic...');
  
  const fs = require('fs');
  const path = require('path');
  
  // Check if we have cached videos to analyze
  const cacheDir = path.join(process.cwd(), 'public', 'video-cache');
  
  if (!fs.existsSync(cacheDir)) {
    console.log('❌ No cached videos found. Run a render request first.');
    return false;
  }
  
  const files = fs.readdirSync(cacheDir);
  if (files.length === 0) {
    console.log('❌ No cached videos found. Run a render request first.');
    return false;
  }
  
  console.log(`📁 Found ${files.length} cached videos:`);
  
  files.forEach(file => {
    const filePath = path.join(cacheDir, file);
    const stats = fs.statSync(filePath);
    const fileSizeKB = (stats.size / 1024).toFixed(2);
    
    // Apply the same logic as getVideoDurationJS
    let estimatedDuration;
    if (stats.size / 1024 < 50) {
      estimatedDuration = 1.5;
    } else if (stats.size / 1024 < 150) {
      estimatedDuration = 2.5;
    } else if (stats.size / 1024 < 500) {
      estimatedDuration = 4.0;
    } else {
      estimatedDuration = 6.0;
    }
    
    console.log(`   📄 ${file}`);
    console.log(`      Size: ${fileSizeKB}KB → Estimated: ${estimatedDuration}s`);
  });
  
  return true;
}

// Main test function
async function runTests() {
  console.log('🚀 DURATION DETECTION IMPROVEMENT TEST\n');
  
  // Test 1: File size estimation
  const fileSizeTest = await testFileSizeEstimation();
  
  if (fileSizeTest) {
    console.log('\n✅ File size estimation is working');
  }
  
  // Test 2: Full render with duration detection
  const renderTest = await testDurationDetection();
  
  // Analysis
  analyzeDurationLogs();
  
  console.log('\n📊 TEST RESULTS:');
  console.log(`   📄 File Size Test: ${fileSizeTest ? '✅ PASS' : '⏭️ SKIP'}`);
  console.log(`   🎬 Render Test: ${renderTest ? '✅ PASS' : '❌ FAIL'}`);
  
  if (renderTest) {
    console.log('\n🎉 DURATION DETECTION IMPROVED!');
    console.log('Your video API now has better duration estimation.');
    console.log('For perfect accuracy, install FFmpeg.');
  }
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testDurationDetection, testFileSizeEstimation };
