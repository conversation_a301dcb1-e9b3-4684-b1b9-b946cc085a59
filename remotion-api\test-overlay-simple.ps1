# Simple overlay test
$body = @{
    clips = @("https://0x0.st/8kgm.mp4")
    overlays = @(
        @{
            src = "https://via.placeholder.com/150x150/FF6B6B/FFFFFF?text=LOGO"
            start = 0
            end = 5
            position = "top-right"
            width = 120
            animation = "fade"
        }
    )
} | ConvertTo-Json -Depth 10

Write-Host "Testing PNG overlay feature..."
Write-Host "Request body:"
Write-Host $body

$response = Invoke-RestMethod -Uri "http://localhost:5001/render" -Method POST -Body $body -ContentType "application/json"

Write-Host "Response:"
$response | ConvertTo-Json -Depth 10

Write-Host "Job ID: $($response.id)"
Write-Host "Check status at: http://localhost:5001/status/$($response.id)"
