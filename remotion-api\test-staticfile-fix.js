const axios = require('axios');

// Test the staticFile fix with a smaller request first
async function testStaticFileFix() {
  console.log('🧪 Testing staticFile fix with cached videos...\n');
  
  // Start with just 2 clips to test the fix
  const testRequest = {
    clips: [
      "https://0x0.st/8n7Z.mp4",
      "https://0x0.st/8n7N.mp4"
    ],
    aspectRatio: "1:1",
    transition: "fade",
    subtitles: [
      {
        text: "Testing cached video fix",
        start: 0.5,
        end: 3.0,
        position: "bottom",
        backgroundColor: "linear-gradient(135deg, rgba(0,0,0,0.9), rgba(30,30,30,0.9))",
        textColor: "#ffffff",
        fontSize: 56,
        transition: "fade",
        transitionDuration: 0.5
      }
    ]
  };
  
  try {
    console.log('📤 Sending render request with 2 clips...');
    const response = await axios.post('http://localhost:5001/render', testRequest, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 5000
    });
    
    console.log('✅ Request accepted:', response.data);
    const jobId = response.data.id;
    
    console.log('\n📊 Monitoring job progress...');
    
    let attempts = 0;
    const maxAttempts = 30; // 2.5 minutes max
    
    while (attempts < maxAttempts) {
      try {
        const statusResponse = await axios.get(`http://localhost:5001/status/${jobId}`, {
          timeout: 5000
        });
        
        const status = statusResponse.data;
        console.log(`⏱️  Attempt ${attempts + 1}: ${status.status} - ${status.progress}`);
        
        if (status.status === 'completed') {
          console.log('\n🎉 SUCCESS! staticFile fix is working!');
          console.log('📊 Final status:', status);
          
          if (status.local_download_url) {
            console.log(`🔗 Download URL: ${status.local_download_url}`);
          }
          
          return true;
        } else if (status.status === 'failed') {
          console.log('\n❌ FAILED! Error details:');
          console.log('Error:', status.error);
          console.log('Steps completed:', status.steps);
          
          // Analyze the error
          if (status.error && status.error.includes('staticFile')) {
            console.log('\n🔍 ANALYSIS: Still getting staticFile errors. Need to check:');
            console.log('- Video caching to public/ directory');
            console.log('- Relative path conversion');
            console.log('- staticFile() usage in VideoMerger component');
          } else if (status.error && status.error.includes('delayRender')) {
            console.log('\n🔍 ANALYSIS: Back to timeout errors - caching may not be working');
          } else {
            console.log('\n🔍 ANALYSIS: Different error - check server logs for details');
          }
          
          return false;
        }
        
        // Wait 5 seconds before next check
        await new Promise(resolve => setTimeout(resolve, 5000));
        attempts++;
        
      } catch (statusError) {
        console.log(`⚠️  Status check error: ${statusError.message}`);
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
    
    console.log('\n⏰ Timeout waiting for job completion');
    return false;
    
  } catch (error) {
    console.log('\n❌ Request failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('🔍 Server is not running. Please start it with: npm run dev');
    }
    
    return false;
  }
}

// Test with the full 8-clip request if the 2-clip test passes
async function testFullRequest() {
  console.log('\n🧪 Testing full 8-clip request...\n');
  
  const fullRequest = {
    clips: [
      "https://0x0.st/8n7Z.mp4",
      "https://0x0.st/8n7N.mp4", 
      "https://0x0.st/8n7q.mp4",
      "https://0x0.st/8nEJ.mp4",
      "https://0x0.st/8n7b.mp4",
      "https://0x0.st/8n7c.mp4",
      "https://0x0.st/8nRv.mp4",
      "https://0x0.st/8nUA.mp4"
    ],
    aspectRatio: "1:1",
    transition: "fade",
    subtitles: [
      {
        text: "Discover eco sip the reusable water bottle that",
        start: 0.35951087,
        end: 3.8747282,
        position: "bottom",
        backgroundColor: "linear-gradient(135deg, rgba(0,0,0,0.9), rgba(30,30,30,0.9))",
        textColor: "#ffffff",
        fontSize: 56,
        transition: "fade",
        transitionDuration: 0.5
      }
    ]
  };
  
  try {
    console.log('📤 Sending full render request with 8 clips...');
    const response = await axios.post('http://localhost:5001/render', fullRequest, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 5000
    });
    
    console.log('✅ Request accepted:', response.data);
    const jobId = response.data.id;
    
    console.log('\n📊 Monitoring full job progress...');
    
    let attempts = 0;
    const maxAttempts = 60; // 5 minutes max for full request
    
    while (attempts < maxAttempts) {
      try {
        const statusResponse = await axios.get(`http://localhost:5001/status/${jobId}`, {
          timeout: 5000
        });
        
        const status = statusResponse.data;
        console.log(`⏱️  Attempt ${attempts + 1}: ${status.status} - ${status.progress}`);
        
        if (status.status === 'completed') {
          console.log('\n🎉 FULL SUCCESS! All 8 clips processed successfully!');
          console.log('📊 Final status:', status);
          return true;
        } else if (status.status === 'failed') {
          console.log('\n❌ Full request failed:', status.error);
          return false;
        }
        
        await new Promise(resolve => setTimeout(resolve, 5000));
        attempts++;
        
      } catch (statusError) {
        console.log(`⚠️  Status check error: ${statusError.message}`);
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
    
    return false;
    
  } catch (error) {
    console.log('\n❌ Full request failed:', error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 STATICFILE FIX TEST SUITE\n');
  console.log('This will test the fix for the staticFile absolute path issue.\n');
  
  // Test 1: Simple 2-clip test
  const simpleTest = await testStaticFileFix();
  
  if (simpleTest) {
    console.log('\n✅ Simple test passed! Proceeding with full test...');
    
    // Test 2: Full 8-clip test
    const fullTest = await testFullRequest();
    
    console.log('\n📊 FINAL TEST RESULTS:');
    console.log(`   🔧 Simple Test (2 clips): ✅ PASS`);
    console.log(`   🎬 Full Test (8 clips): ${fullTest ? '✅ PASS' : '❌ FAIL'}`);
    
    if (fullTest) {
      console.log('\n🎉 ALL TESTS PASSED! The staticFile fix is working perfectly!');
      console.log('Your video API is now fully functional with:');
      console.log('- ✅ Remote video caching');
      console.log('- ✅ Proper staticFile() usage');
      console.log('- ✅ 1:1 aspect ratio support');
      console.log('- ✅ Timeout prevention');
    }
  } else {
    console.log('\n📊 TEST RESULTS:');
    console.log(`   🔧 Simple Test (2 clips): ❌ FAIL`);
    console.log(`   🎬 Full Test (8 clips): ⏭️ SKIPPED`);
    console.log('\n⚠️  Simple test failed. Check the error details above.');
  }
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testStaticFileFix, testFullRequest };
