const axios = require('axios');

// Test the new clipDurations feature
async function testClipDurations() {
  console.log('🧪 Testing NEW clipDurations feature...\n');
  
  // Test with exact durations for each clip
  const testRequest = {
    clips: [
      "https://0x0.st/8n7Z.mp4",  // Specify 1.8s
      "https://0x0.st/8n7N.mp4",  // Specify 2.1s  
      "https://0x0.st/8n7q.mp4"   // Specify 1.5s
    ],
    clipDurations: [1.8, 2.1, 1.5], // NEW: Exact durations for each clip
    aspectRatio: "1:1",
    transition: "fade",
    subtitles: [
      {
        text: "Perfect timing with exact durations!",
        start: 0.5,
        end: 4.0,
        position: "bottom",
        backgroundColor: "linear-gradient(135deg, rgba(0,0,0,0.9), rgba(30,30,30,0.9))",
        textColor: "#ffffff",
        fontSize: 48,
        transition: "fade"
      }
    ]
  };
  
  try {
    console.log('📤 Sending render request with exact clip durations...');
    console.log('   🎞️  Clip 1: 1.8s duration');
    console.log('   🎞️  Clip 2: 2.1s duration');
    console.log('   🎞️  Clip 3: 1.5s duration');
    console.log('   ⏱️  Total expected: 5.4s of clips');
    console.log('   📝 Subtitle timing: 0.5s - 4.0s');
    console.log('   🎯 Expected final duration: ~4.0s (subtitle-based)\n');
    
    const response = await axios.post('http://localhost:5001/render', testRequest, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 5000
    });
    
    console.log('✅ Request accepted:', response.data);
    const jobId = response.data.id;
    
    console.log('\n📊 Monitoring job progress (watching for exact durations)...');
    
    let attempts = 0;
    const maxAttempts = 25; // ~2 minutes
    
    while (attempts < maxAttempts) {
      try {
        const statusResponse = await axios.get(`http://localhost:5001/status/${jobId}`, {
          timeout: 5000
        });
        
        const status = statusResponse.data;
        console.log(`⏱️  Attempt ${attempts + 1}: ${status.status} - ${status.progress}`);
        
        if (status.status === 'completed') {
          console.log('\n🎉 SUCCESS! Video completed with exact clip durations!');
          console.log('📊 Final status:', status);
          
          if (status.local_download_url) {
            console.log(`🔗 Download URL: ${status.local_download_url}`);
          }
          
          console.log('\n✅ EXPECTED RESULTS:');
          console.log('   📊 Server logs should show:');
          console.log('      "✅ Using provided duration: 1.8s for clip 1"');
          console.log('      "✅ Using provided duration: 2.1s for clip 2"');
          console.log('      "✅ Using provided duration: 1.5s for clip 3"');
          console.log('   🎯 Final video should be ~4.0s (subtitle-based timing)');
          
          return true;
        } else if (status.status === 'failed') {
          console.log('\n❌ FAILED! Error details:');
          console.log('Error:', status.error);
          return false;
        }
        
        // Wait 5 seconds before next check
        await new Promise(resolve => setTimeout(resolve, 5000));
        attempts++;
        
      } catch (statusError) {
        console.log(`⚠️  Status check error: ${statusError.message}`);
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
    
    console.log('\n⏰ Timeout waiting for job completion');
    return false;
    
  } catch (error) {
    console.log('\n❌ Request failed:', error.message);
    return false;
  }
}

// Test mixed mode: some durations provided, some auto-detected
async function testMixedDurations() {
  console.log('\n🧪 Testing mixed duration mode...\n');
  
  const testRequest = {
    clips: [
      "https://0x0.st/8n7Z.mp4",  // Provide duration
      "https://0x0.st/8n7N.mp4"   // Let it auto-detect
    ],
    clipDurations: [2.0], // Only provide duration for first clip
    aspectRatio: "1:1",
    subtitles: [
      {
        text: "Mixed duration test",
        start: 0.5,
        end: 3.5,
        position: "bottom"
      }
    ]
  };
  
  try {
    console.log('📤 Testing mixed mode: first clip 2.0s, second auto-detect...');
    
    const response = await axios.post('http://localhost:5001/render', testRequest, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 5000
    });
    
    console.log('✅ Mixed mode request accepted:', response.data);
    return true;
    
  } catch (error) {
    console.log('❌ Mixed mode failed:', error.message);
    return false;
  }
}

// Compare old vs new approach
function showComparison() {
  console.log('\n📊 OLD vs NEW APPROACH COMPARISON\n');
  
  console.log('❌ OLD APPROACH (Auto-detection):');
  console.log('   🔍 Requires ffprobe installation');
  console.log('   ⏰ Slower processing (duration detection step)');
  console.log('   📏 Uses 2.5s fallbacks when detection fails');
  console.log('   🎲 Unpredictable results with network issues');
  console.log('');
  
  console.log('✅ NEW APPROACH (Exact durations):');
  console.log('   🎯 Perfect control over clip timing');
  console.log('   ⚡ Faster processing (skips detection)');
  console.log('   📊 Exact durations as specified');
  console.log('   🔒 Reliable results every time');
  console.log('   🚫 No ffprobe dependency');
  console.log('');
  
  console.log('📝 EXAMPLE REQUEST:');
  console.log('```json');
  console.log('{');
  console.log('  "clips": ["video1.mp4", "video2.mp4", "video3.mp4"],');
  console.log('  "clipDurations": [1.8, 2.1, 1.5],  // NEW!');
  console.log('  "aspectRatio": "1:1",');
  console.log('  "subtitles": [...]');
  console.log('}');
  console.log('```');
}

// Main test function
async function runTests() {
  console.log('🚀 CLIP DURATIONS FEATURE TEST SUITE\n');
  console.log('Testing the new clipDurations parameter for exact timing control.\n');
  
  // Show comparison
  showComparison();
  
  // Test 1: Exact durations
  const exactTest = await testClipDurations();
  
  // Test 2: Mixed mode
  const mixedTest = await testMixedDurations();
  
  console.log('\n📊 TEST RESULTS:');
  console.log(`   🎯 Exact Durations Test: ${exactTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   🔀 Mixed Mode Test: ${mixedTest ? '✅ PASS' : '❌ FAIL'}`);
  
  if (exactTest) {
    console.log('\n🎉 CLIP DURATIONS FEATURE IS WORKING!');
    console.log('You now have perfect control over video timing.');
    console.log('');
    console.log('🎯 NEXT STEPS:');
    console.log('1. Use clipDurations in your production requests');
    console.log('2. Measure your actual clip durations beforehand');
    console.log('3. Enjoy faster, more reliable video processing!');
  } else {
    console.log('\n⚠️  Feature test failed. Check server logs for details.');
  }
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testClipDurations, testMixedDurations };
