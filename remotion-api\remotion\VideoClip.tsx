import React, { useEffect, useState } from 'react';
import { Video, staticFile, delayRender, continueRender } from 'remotion';

interface VideoClipProps {
  src: string;
  style?: React.CSSProperties;
  onDurationDetected?: (duration: number) => void;
}

export const VideoClip: React.FC<VideoClipProps> = ({ 
  src, 
  style = {}, 
  onDurationDetected 
}) => {
  const [handle] = useState(() => delayRender());
  const [duration, setDuration] = useState<number | null>(null);

  useEffect(() => {
    const video = document.createElement('video');
    video.crossOrigin = 'anonymous';
    
    const handleLoadedMetadata = () => {
      const videoDuration = video.duration;
      setDuration(videoDuration);
      if (onDurationDetected) {
        onDurationDetected(videoDuration);
      }
      continueRender(handle);
    };

    const handleError = () => {
      console.warn(`Failed to load video metadata for: ${src}`);
      // Use default duration of 5 seconds if metadata fails
      setDuration(5);
      if (onDurationDetected) {
        onDurationDetected(5);
      }
      continueRender(handle);
    };

    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('error', handleError);
    
    video.src = src.startsWith('http') ? src : staticFile(src);

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('error', handleError);
    };
  }, [src, handle, onDurationDetected]);

  return (
    <Video 
      src={src.startsWith('http') ? src : staticFile(src)} 
      style={{
        width: '100%',
        height: '100%',
        objectFit: 'cover',
        ...style,
      }}
    />
  );
};
