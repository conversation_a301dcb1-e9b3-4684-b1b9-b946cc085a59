{"examples": [{"name": "Rainbow Gradient Showcase", "description": "Demonstrates rainbow gradient with bounce animation", "request": {"clips": ["https://0x0.st/8kgm.mp4"], "subtitles": [{"text": "🌈 Rainbow Magic!", "start": 0, "end": 3, "backgroundColor": "rainbow", "textColor": "white", "fontSize": 64, "position": "top", "transition": "bounce", "transitionDuration": 1.0}]}}, {"name": "All Preset Gradients", "description": "Shows all available preset gradient backgrounds", "request": {"clips": [], "subtitles": [{"text": "🔴 Red Gradient", "start": 0, "end": 2, "backgroundColor": "red", "position": "top", "transition": "slide-down"}, {"text": "🔵 Blue Gradient", "start": 2, "end": 4, "backgroundColor": "blue", "position": "center", "transition": "fade"}, {"text": "🟣 Purple Gradient", "start": 4, "end": 6, "backgroundColor": "purple", "position": "bottom", "transition": "slide-up"}, {"text": "🟢 Green Gradient", "start": 6, "end": 8, "backgroundColor": "green", "position": "top", "transition": "zoom-in"}, {"text": "🟠 Orange Gradient", "start": 8, "end": 10, "backgroundColor": "orange", "position": "center", "transition": "zoom-out"}, {"text": "🩷 Pink Gradient", "start": 10, "end": 12, "backgroundColor": "pink", "position": "bottom", "transition": "slide-left"}]}}, {"name": "Special Effect Gradients", "description": "Showcases special preset gradients with effects", "request": {"clips": ["https://0x0.st/8kgm.mp4"], "subtitles": [{"text": "🌅 Sunset Vibes", "start": 0, "end": 3, "backgroundColor": "sunset", "position": "top", "transition": "fade", "transitionDuration": 0.8}, {"text": "🌊 Ocean Depths", "start": 3, "end": 6, "backgroundColor": "ocean", "position": "center", "transition": "slide-right"}, {"text": "🔥 Fire Power", "start": 6, "end": 9, "backgroundColor": "fire", "position": "bottom", "transition": "zoom-in"}, {"text": "✨ <PERSON><PERSON>", "start": 9, "end": 12, "backgroundColor": "neon", "position": "center", "transition": "bounce"}, {"text": "🌌 Galaxy Far Away", "start": 12, "end": 15, "backgroundColor": "galaxy", "position": "top", "transition": "slide-down"}]}}, {"name": "Typewriter Effect Demo", "description": "Demonstrates typewriter animation with different speeds", "request": {"clips": [], "subtitles": [{"text": "This text appears slowly...", "start": 0, "end": 5, "backgroundColor": "linear-gradient(135deg, #667eea, #764ba2)", "fontSize": 48, "position": "center", "transition": "typewriter", "transitionDuration": 4.0}, {"text": "This one is faster!", "start": 6, "end": 9, "backgroundColor": "neon", "fontSize": 52, "position": "bottom", "transition": "typewriter", "transitionDuration": 1.5}]}}, {"name": "Custom CSS Gradients", "description": "Shows custom linear gradients with different angles", "request": {"clips": ["https://0x0.st/8kgm.mp4"], "subtitles": [{"text": "Custom Diagonal Gradient", "start": 0, "end": 3, "backgroundColor": "linear-gradient(45deg, #ff6b6b, #4ecdc4)", "textColor": "white", "position": "top"}, {"text": "Vertical Gradient", "start": 3, "end": 6, "backgroundColor": "linear-gradient(to bottom, #667eea, #764ba2)", "textColor": "#ffffff", "position": "center"}, {"text": "Multi-Color Gradient", "start": 6, "end": 9, "backgroundColor": "linear-gradient(135deg, #ff9a9e, #fecfef, #fecfef, #667eea)", "textColor": "white", "position": "bottom"}]}}, {"name": "Arabic Text Support", "description": "Demonstrates Arabic RTL text with custom styling", "request": {"clips": [], "subtitles": [{"text": "مرحبا بكم في الفيديو المحسن", "start": 0, "end": 4, "backgroundColor": "galaxy", "textColor": "#ffffff", "fontSize": 60, "position": "center", "transition": "fade"}, {"text": "النص العربي مدعوم بالكامل", "start": 4, "end": 8, "backgroundColor": "rainbow", "textColor": "white", "fontSize": 56, "position": "bottom", "transition": "slide-up"}]}}, {"name": "All Transition Effects", "description": "Showcases all available transition animations", "request": {"clips": ["https://0x0.st/8kgm.mp4"], "subtitles": [{"text": "No Transition", "start": 0, "end": 2, "backgroundColor": "red", "transition": "none"}, {"text": "Fade Effect", "start": 2, "end": 4, "backgroundColor": "blue", "transition": "fade"}, {"text": "Slide Up", "start": 4, "end": 6, "backgroundColor": "green", "transition": "slide-up"}, {"text": "Zoom In", "start": 6, "end": 8, "backgroundColor": "purple", "transition": "zoom-in"}, {"text": "Bounce!", "start": 8, "end": 10, "backgroundColor": "orange", "transition": "bounce"}]}}]}