const axios = require('axios');

// Test the path fix with a single clip first
async function testPathFix() {
  console.log('🧪 Testing path fix for cached videos...\n');
  
  // Start with just 1 clip to test the path fix
  const testRequest = {
    clips: [
      "https://0x0.st/8n7Z.mp4"
    ],
    aspectRatio: "1:1",
    transition: "fade",
    subtitles: [
      {
        text: "Testing path fix",
        start: 0.5,
        end: 2.5,
        position: "bottom",
        backgroundColor: "rgba(0,0,0,0.8)",
        textColor: "#ffffff",
        fontSize: 48
      }
    ]
  };
  
  try {
    console.log('📤 Sending render request with 1 clip...');
    const response = await axios.post('http://localhost:5001/render', testRequest, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 5000
    });
    
    console.log('✅ Request accepted:', response.data);
    const jobId = response.data.id;
    
    console.log('\n📊 Monitoring job progress...');
    
    let attempts = 0;
    const maxAttempts = 20; // 100 seconds max
    
    while (attempts < maxAttempts) {
      try {
        const statusResponse = await axios.get(`http://localhost:5001/status/${jobId}`, {
          timeout: 5000
        });
        
        const status = statusResponse.data;
        console.log(`⏱️  Attempt ${attempts + 1}: ${status.status} - ${status.progress}`);
        
        if (status.status === 'completed') {
          console.log('\n🎉 SUCCESS! Path fix is working!');
          console.log('📊 Final status:', status);
          
          if (status.local_download_url) {
            console.log(`🔗 Download URL: ${status.local_download_url}`);
          }
          
          return true;
        } else if (status.status === 'failed') {
          console.log('\n❌ FAILED! Error details:');
          console.log('Error:', status.error);
          console.log('Steps completed:', status.steps);
          
          // Analyze the error
          if (status.error && status.error.includes('404')) {
            console.log('\n🔍 ANALYSIS: Still getting 404 errors. Issues:');
            console.log('- Video files not accessible to Remotion');
            console.log('- Path format still incorrect');
            console.log('- Build directory not properly configured');
          } else if (status.error && status.error.includes('delayRender')) {
            console.log('\n🔍 ANALYSIS: Still getting timeout errors:');
            console.log('- Videos not loading properly');
            console.log('- Path resolution failing');
          } else if (status.error && status.error.includes('staticFile')) {
            console.log('\n🔍 ANALYSIS: staticFile errors persist:');
            console.log('- Path conversion not working');
            console.log('- Directory structure issues');
          } else {
            console.log('\n🔍 ANALYSIS: Different error - check server logs');
          }
          
          return false;
        }
        
        // Wait 5 seconds before next check
        await new Promise(resolve => setTimeout(resolve, 5000));
        attempts++;
        
      } catch (statusError) {
        console.log(`⚠️  Status check error: ${statusError.message}`);
        attempts++;
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
    
    console.log('\n⏰ Timeout waiting for job completion');
    return false;
    
  } catch (error) {
    console.log('\n❌ Request failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('🔍 Server is not running. Please start it with: npm run dev');
    }
    
    return false;
  }
}

// Check if cached videos are accessible
async function checkVideoAccess() {
  console.log('\n🔍 Checking video file access...');
  
  const fs = require('fs');
  const path = require('path');
  
  // Check if directories exist
  const publicDir = path.join(process.cwd(), 'public', 'video-cache');
  const buildDir = path.join(process.cwd(), 'build', 'public', 'video-cache');
  
  console.log(`📁 Public cache dir: ${publicDir}`);
  console.log(`   Exists: ${fs.existsSync(publicDir)}`);
  if (fs.existsSync(publicDir)) {
    const files = fs.readdirSync(publicDir);
    console.log(`   Files: ${files.length} files`);
  }
  
  console.log(`📁 Build cache dir: ${buildDir}`);
  console.log(`   Exists: ${fs.existsSync(buildDir)}`);
  if (fs.existsSync(buildDir)) {
    const files = fs.readdirSync(buildDir);
    console.log(`   Files: ${files.length} files`);
  }
  
  // Test if we can access a video via HTTP
  try {
    const testResponse = await axios.head('http://localhost:5001/public/test-logo.svg', {
      timeout: 3000
    });
    console.log(`✅ Public directory accessible via HTTP: ${testResponse.status}`);
  } catch (error) {
    console.log(`❌ Public directory not accessible: ${error.message}`);
  }
}

// Main test function
async function runTests() {
  console.log('🚀 PATH FIX TEST SUITE\n');
  console.log('This will test the fix for video path and 404 issues.\n');
  
  // Check 1: File access
  await checkVideoAccess();
  
  // Test 2: Single clip render
  const pathTest = await testPathFix();
  
  console.log('\n📊 TEST RESULTS:');
  console.log(`   🔧 Path Fix Test: ${pathTest ? '✅ PASS' : '❌ FAIL'}`);
  
  if (pathTest) {
    console.log('\n🎉 PATH FIX SUCCESSFUL!');
    console.log('The video caching and path resolution is now working correctly.');
    console.log('You can now test with multiple clips and full requests.');
  } else {
    console.log('\n⚠️  Path fix test failed. Check the error analysis above.');
  }
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testPathFix, checkVideoAccess };
