// See all configuration options: https://remotion.dev/docs/config
// Each option also is available as a CLI flag: https://remotion.dev/docs/cli

// Note: When using the Node.JS APIs, the config file doesn't apply. Instead, pass options directly to the APIs

import { Config } from "@remotion/cli/config";

Config.setVideoImageFormat("jpeg");
Config.setOverwriteOutput(true);

// Increase timeout for remote video loading
Config.setDelayRenderTimeoutInMilliseconds(60000); // 60 seconds

// Configure Chromium for better remote video handling
Config.setChromiumOpenGlRenderer("egl");
Config.setBrowserExecutable(null); // Use bundled Chromium
