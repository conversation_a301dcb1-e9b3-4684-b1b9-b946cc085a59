#!/usr/bin/env node

/**
 * Test script for enhanced subtitle features
 * Run: node test-enhanced-subtitles.js
 */

const examples = [
  {
    name: "Rainbow Gradient with Bounce Effect",
    request: {
      clips: ["https://0x0.st/8kgm.mp4"],
      subtitles: [
        {
          text: "🌈 Rainbow Magic!",
          start: 0,
          end: 3,
          backgroundColor: "rainbow",
          textColor: "white",
          position: "top",
          transition: "bounce",
          transitionDuration: 0.8
        }
      ]
    }
  },
  {
    name: "Custom Gradient with Typewriter Effect",
    request: {
      clips: [],
      subtitles: [
        {
          text: "This text appears letter by letter...",
          start: 0,
          end: 5,
          backgroundColor: "linear-gradient(135deg, #667eea, #764ba2)",
          textColor: "#ffffff",
          fontSize: 56,
          position: "center",
          transition: "typewriter",
          transitionDuration: 3.0
        }
      ]
    }
  },
  {
    name: "Multiple Styled Subtitles",
    request: {
      clips: ["https://0x0.st/8kgm.mp4"],
      subtitles: [
        {
          text: "🔥 Fire Background",
          start: 0,
          end: 2,
          backgroundColor: "fire",
          position: "top",
          transition: "slide-down"
        },
        {
          text: "🌊 Ocean Vibes",
          start: 2.5,
          end: 4.5,
          backgroundColor: "ocean",
          position: "center",
          transition: "zoom-in"
        },
        {
          text: "✨ Neon Glow",
          start: 5,
          end: 7,
          backgroundColor: "neon",
          position: "bottom",
          transition: "slide-up"
        }
      ]
    }
  },
  {
    name: "Arabic Text with Custom Styling",
    request: {
      clips: [],
      subtitles: [
        {
          text: "مرحبا بكم في الفيديو المحسن",
          start: 0,
          end: 4,
          backgroundColor: "galaxy",
          textColor: "#ffffff",
          fontSize: 60,
          position: "center",
          transition: "fade",
          transitionDuration: 0.5
        }
      ]
    }
  }
];

console.log("🎬 Enhanced Subtitle Test Examples\n");
console.log("Copy and paste these curl commands to test the new features:\n");

examples.forEach((example, index) => {
  console.log(`${index + 1}. ${example.name}`);
  console.log("─".repeat(50));
  
  const curlCommand = `curl -X POST http://localhost:5001/render \\
  -H "Content-Type: application/json" \\
  -d '${JSON.stringify(example.request, null, 2).replace(/'/g, "\\'")}'`;
  
  console.log(curlCommand);
  console.log("\n");
});

console.log("🎨 Available Background Presets:");
console.log("red, blue, purple, green, orange, pink, cyan, yellow");
console.log("rainbow, sunset, ocean, fire, neon, galaxy");

console.log("\n🎭 Available Transitions:");
console.log("none, fade, slide-up, slide-down, slide-left, slide-right");
console.log("zoom-in, zoom-out, bounce, typewriter");

console.log("\n📍 Available Positions:");
console.log("top, center, bottom");

console.log("\n💡 Pro Tips:");
console.log("• Use 'rainbow' for colorful gradients");
console.log("• Try 'typewriter' with longer transitionDuration (2-3 seconds)");
console.log("• Combine 'bounce' with 'top' position for attention-grabbing intros");
console.log("• Use custom gradients: 'linear-gradient(135deg, #color1, #color2)'");
console.log("• Arabic text is automatically detected and styled with RTL support");
