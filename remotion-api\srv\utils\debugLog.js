/**
 * Logs messages with timestamp, level and jobId
 * @param {string} jobId
 * @param {string} message
 * @param {"info"|"warn"|"error"} [level="info"]
 */
function logDebug(jobId, message, level = "info") {
  const prefix = `[${new Date().toISOString()}] [${level.toUpperCase()}] [Job ${jobId}]`;
  if (level === "error") console.error(`${prefix} ${message}`);
  else if (level === "warn") console.warn(`${prefix} ${message}`);
  else console.log(`${prefix} ${message}`);
}

module.exports = { logDebug };
