# Video Files

Place your video files (8klQ.mp4, 8klP(1).mp4, etc.) in this directory.

The video files should be:
- MP4 format
- 1920x1080 resolution (or any even-numbered dimensions)
- Compatible with H264 codec

## Current Issue
The error "width of NaNpx" occurs because:
1. The VideoMerger composition was not registered in Root.tsx (FIXED)
2. Video files are missing from this directory
3. The renderMedia call was missing proper composition configuration (FIXED)

## Solution
1. Add your video files to this directory
2. Ensure video files have even-numbered dimensions (divisible by 2)
3. The VideoMerger composition is now properly registered with 1920x1080 dimensions
