# 🎬 Remotion Video Merger API v2.0

## 🌐 **Primary Feature: Remote URL Support**

This API is designed to work primarily with **remote video URLs** (hosted videos) while supporting local files for testing.

---

## 📋 **API Endpoints**

### **POST /render** - Create Video
Merge videos with subtitles and audio

### **GET /status/:id** - Check Status  
Monitor rendering progress

### **GET /downloads/:id.mp4** - Download Video
Get rendered video file

### **GET /health** - API Health
Check API status and features

---

## ⏱️ **NEW: Specify Exact Clip Durations**

You can now specify the exact duration for each clip in your request, eliminating the need for automatic duration detection:

```bash
curl -X POST http://localhost:5001/render \
  -H "Content-Type: application/json" \
  -d '{
    "clips": [
      "https://0x0.st/8n7Z.mp4",
      "https://0x0.st/8n7N.mp4",
      "https://0x0.st/8n7q.mp4"
    ],
    "clipDurations": [1.8, 2.1, 1.5],
    "aspectRatio": "1:1",
    "subtitles": [
      {
        "text": "Perfect timing with exact durations!",
        "start": 0.5,
        "end": 4.0,
        "position": "bottom"
      }
    ]
  }'
```

**Benefits:**
- ✅ **Exact Control**: Specify precise duration for each clip
- ✅ **No Detection Needed**: Bypasses ffprobe/duration detection entirely
- ✅ **Perfect Timing**: Ensures videos match your exact requirements
- ✅ **Faster Processing**: Eliminates duration detection step

**Usage:**
- `clipDurations` array must match the number of clips
- Durations are in seconds (e.g., 1.5 = 1.5 seconds)
- If not provided, falls back to automatic detection

---

## 🎚️ **NEW: Audio Volume Controls**

You can now control the volume of voice-over and background music separately:

```bash
curl -X POST http://localhost:5001/render \
  -H "Content-Type: application/json" \
  -d '{
    "clips": [
      "https://0x0.st/8n7Z.mp4",
      "https://0x0.st/8n7N.mp4"
    ],
    "clipDurations": [2.0, 2.5],
    "voiceOver": "https://0x0.st/85oT.mp3",
    "voiceOverVolume": 1.2,
    "music": "https://example.com/background.mp3",
    "musicVolume": 0.2,
    "aspectRatio": "1:1",
    "subtitles": [
      {
        "text": "Perfect audio mix with custom volumes!",
        "start": 0.5,
        "end": 3.5,
        "position": "bottom"
      }
    ]
  }'
```

**Volume Parameters:**
- **`voiceOverVolume`**: 0.0 to 2.0 (default: 1.0)
  - 0.0 = Silent
  - 1.0 = Normal volume (default)
  - 2.0 = Double volume
- **`musicVolume`**: 0.0 to 2.0 (default: 0.3)
  - 0.0 = Silent
  - 0.3 = Quiet background (default)
  - 1.0 = Normal volume

**Benefits:**
- ✅ **Perfect Audio Balance**: Mix voice-over and music to your preference
- ✅ **Professional Control**: Fine-tune audio levels like a pro
- ✅ **Flexible Mixing**: Boost voice-over or reduce background music as needed

---

## 🎯 **Primary Use Case: Remote URLs**

### **Basic Remote Video Merge**
```bash
curl -X POST http://localhost:5001/render \
  -H "Content-Type: application/json" \
  -d '{
    "clips": [
      "https://0x0.st/8kgm.mp4",
      "https://0x0.st/8kIP.mp4"
    ],
    "subtitles": [
      {"text": "Hello!", "start": 0, "end": 3}
    ]
  }'
```

### **Full-Featured Request with Subtitles & Overlays**
```bash
curl -X POST http://localhost:5001/render \
  -H "Content-Type: application/json" \
  -d '{
    "clips": [
      "https://example.com/video1.mp4",
      "https://example.com/video2.mp4"
    ],
    "voiceOver": "https://example.com/narration.mp3",
    "music": "https://example.com/background.mp3",
    "aspectRatio": "16:9",
    "transition": "fade",
    "subtitles": [
      {
        "text": "Introduction",
        "start": 0,
        "end": 5,
        "backgroundColor": "rainbow",
        "textColor": "white",
        "position": "bottom",
        "transition": "slide-up",
        "transitionDuration": 0.5
      }
    ],
    "overlays": [
      {
        "src": "https://example.com/logo.png",
        "start": 0,
        "end": 10,
        "position": "top-right",
        "width": 150,
        "opacity": 0.9,
        "animation": "fade"
      },
      {
        "src": "https://example.com/cta-button.png",
        "start": 8,
        "end": 15,
        "position": "bottom-center",
        "width": 300,
        "animation": "bounce",
        "animationDuration": 1.0
      }
    ]
  }'
```

---

## 🖼️ **Image Overlay Features (NEW!)**

Add PNG logos, call-to-action buttons, watermarks, and other transparent images to your videos!

### **Overlay Positions**
- `top-left`, `top-center`, `top-right`
- `center-left`, `center`, `center-right`
- `bottom-left`, `bottom-center`, `bottom-right`

### **Animation Effects**
- `none` - No animation
- `fade` - Fade in/out (default)
- `slide-in` - Slide from right
- `zoom-in` - Zoom in effect
- `bounce` - Bounce in effect
- `pulse` - Continuous pulsing
- `rotate` - Continuous rotation

### **Logo Overlay Example**
```bash
curl -X POST http://localhost:5001/render \
  -H "Content-Type: application/json" \
  -d '{
    "clips": ["https://0x0.st/8kgm.mp4"],
    "overlays": [
      {
        "src": "https://example.com/logo.png",
        "start": 0,
        "end": 8,
        "position": "top-right",
        "width": 150,
        "opacity": 0.8,
        "animation": "fade",
        "offsetX": -20,
        "offsetY": 20
      }
    ]
  }'
```

### **Call-to-Action Button Example**
```bash
curl -X POST http://localhost:5001/render \
  -H "Content-Type: application/json" \
  -d '{
    "clips": ["https://0x0.st/8kgm.mp4"],
    "overlays": [
      {
        "src": "https://example.com/subscribe-button.png",
        "start": 5,
        "end": 8,
        "position": "bottom-center",
        "width": 250,
        "animation": "bounce",
        "animationDuration": 1.2
      }
    ]
  }'
```

### **Multiple Overlays Example**
```bash
curl -X POST http://localhost:5001/render \
  -H "Content-Type: application/json" \
  -d '{
    "clips": ["https://0x0.st/8kgm.mp4"],
    "overlays": [
      {
        "src": "https://example.com/logo.png",
        "start": 0,
        "end": 8,
        "position": "top-left",
        "width": 120,
        "animation": "fade"
      },
      {
        "src": "https://example.com/watermark.png",
        "start": 0,
        "end": 8,
        "position": "bottom-right",
        "width": 100,
        "opacity": 0.6,
        "animation": "none"
      },
      {
        "src": "https://example.com/cta.png",
        "start": 6,
        "end": 8,
        "position": "center",
        "width": 300,
        "animation": "zoom-in"
      }
    ]
  }'
```

---

## 🎨 **Enhanced Subtitle Features**

### **Background Colors & Gradients**

#### **Preset Colors:**
- `red`, `blue`, `purple`, `green`, `orange`, `pink`, `cyan`, `yellow`
- `rainbow`, `sunset`, `ocean`, `fire`, `neon`, `galaxy`

#### **Custom Colors:**
- Solid colors: `#ff0000`, `rgba(255,0,0,0.8)`
- CSS gradients: `linear-gradient(135deg, #ff6b6b, #4ecdc4)`

#### **Examples:**
```bash
# Preset gradient
"backgroundColor": "rainbow"

# Custom gradient
"backgroundColor": "linear-gradient(135deg, #667eea, #764ba2)"

# Solid color with transparency
"backgroundColor": "rgba(255,0,0,0.8)"
```

### **Subtitle Positions**
- `top` - Top of the video
- `center` - Center of the video
- `bottom` - Bottom of the video (default)

### **Transition Effects**
- `none` - No transition
- `fade` - Fade in/out (default)
- `slide-up` - Slide from bottom
- `slide-down` - Slide from top
- `slide-left` - Slide from right
- `slide-right` - Slide from left
- `zoom-in` - Zoom in effect
- `zoom-out` - Zoom out effect
- `bounce` - Bounce in effect
- `typewriter` - Typewriter text effect

### **Complete Subtitle Example**
```bash
curl -X POST http://localhost:5001/render \
  -H "Content-Type: application/json" \
  -d '{
    "clips": ["https://0x0.st/8kgm.mp4"],
    "subtitles": [
      {
        "text": "🌟 Welcome to our video!",
        "start": 0,
        "end": 3,
        "backgroundColor": "rainbow",
        "textColor": "white",
        "fontSize": 60,
        "position": "top",
        "transition": "bounce",
        "transitionDuration": 0.8
      },
      {
        "text": "Custom gradient background",
        "start": 4,
        "end": 7,
        "backgroundColor": "linear-gradient(135deg, #667eea, #764ba2)",
        "textColor": "#ffffff",
        "position": "center",
        "transition": "slide-left"
      },
      {
        "text": "Typewriter effect is cool!",
        "start": 8,
        "end": 12,
        "backgroundColor": "neon",
        "transition": "typewriter",
        "transitionDuration": 2.0
      }
    ]
  }'
```

---

## 🧪 **Testing with Local Files**

For testing purposes, you can use local files:

### **1. Add files to public directory:**
```
remotion-api/public/test-video.mp4
remotion-api/public/test-audio.mp3
```

### **2. Use local filenames:**
```bash
curl -X POST http://localhost:5001/render \
  -H "Content-Type: application/json" \
  -d '{
    "clips": ["test-video.mp4"],
    "subtitles": [{"text": "Local test", "start": 0, "end": 3}]
  }'
```

---

## 📊 **Response Flow**

### **1. Initial Response**
```json
{
  "id": "abc123-def456-ghi789",
  "status": "/status/abc123-def456-ghi789",
  "message": "Render job started successfully"
}
```

### **2. Processing Status**
```bash
curl http://localhost:5001/status/abc123-def456-ghi789
```

```json
{
  "status": "processing",
  "progress": "Remotion rendering started",
  "steps": ["Job created", "Processing clips", "Remotion rendering started"]
}
```

### **3. Completed Status**
```json
{
  "status": "completed",
  "progress": "Complete",
  "local_download_url": "http://localhost:5001/downloads/abc123-def456-ghi789.mp4",
  "download_url": "https://0x0.st/xyz.mp4",
  "completedAt": "2025-07-22T19:15:30.123Z"
}
```

---

## 🎬 **Video Specifications**

### **Output Format:**
- **Resolution:** 1920x1080 (Full HD)
- **Codec:** H264
- **Frame Rate:** 30 FPS
- **Duration:** 5 seconds per clip + subtitle timing

### **Input Support:**
- **Remote Videos:** Any publicly accessible HTTPS URL
- **Local Videos:** MP4 files in public/ directory
- **Audio:** MP3, WAV (remote URLs or local files)
- **Subtitles:** Text with start/end timing in seconds

---

## ✅ **Supported URL Formats**

### **✅ Remote URLs (Primary)**
- `https://0x0.st/8kgm.mp4`
- `https://example.com/video.mp4`
- `https://cdn.example.com/media/clip.mp4`

### **✅ Local Files (Testing)**
- `video.mp4` (must exist in public/ directory)
- `audio.mp3` (must exist in public/ directory)

---

## 🚀 **Quick Start Examples**

### **Your Hosted Videos:**
```bash
curl -X POST http://localhost:5001/render \
  -H "Content-Type: application/json" \
  -d '{
    "clips": ["https://0x0.st/8kgm.mp4", "https://0x0.st/8kIP.mp4"],
    "subtitles": [{"text": "Hello!", "start": 0, "end": 3}]
  }'
```

### **Subtitle-Only Video with Styling:**
```bash
curl -X POST http://localhost:5001/render \
  -H "Content-Type: application/json" \
  -d '{
    "clips": [],
    "subtitles": [
      {
        "text": "🎉 Welcome!",
        "start": 0,
        "end": 3,
        "backgroundColor": "rainbow",
        "transition": "bounce"
      },
      {
        "text": "This is a styled text video",
        "start": 4,
        "end": 8,
        "backgroundColor": "linear-gradient(135deg, #ff6b6b, #4ecdc4)",
        "position": "center",
        "transition": "typewriter",
        "transitionDuration": 2.0
      }
    ]
  }'
```

### **Quick Color Examples:**
```bash
# Rainbow gradient
"backgroundColor": "rainbow"

# Fire gradient
"backgroundColor": "fire"

# Custom blue gradient
"backgroundColor": "linear-gradient(135deg, #74b9ff, #0984e3)"

# Semi-transparent red
"backgroundColor": "rgba(255,0,0,0.7)"
```

---

## 🎯 **API Features**

✅ **Remote URL Support** - Primary feature for hosted videos
✅ **Local File Testing** - For development and testing
✅ **Enhanced Subtitles** - Rich styling with colors, gradients & transitions
✅ **Image Overlays** - PNG logos, call-to-action buttons, watermarks
✅ **Audio Mixing** - Voice-over and background music
✅ **Multiple Downloads** - Local and external hosting
✅ **Progress Tracking** - Real-time status updates
✅ **Error Handling** - Detailed error messages

### **🎨 Subtitle Features:**
✅ **15+ Preset Gradients** - rainbow, sunset, ocean, fire, neon, galaxy, etc.
✅ **Custom CSS Gradients** - Full linear-gradient support
✅ **Solid Colors** - Hex, RGB, RGBA color support
✅ **10 Transition Effects** - fade, slide, zoom, bounce, typewriter, etc.
✅ **3 Position Options** - top, center, bottom placement
✅ **Multi-language Support** - Arabic RTL text with proper fonts
✅ **Custom Styling** - fontSize, textColor, transitionDuration
✅ **Advanced Effects** - backdrop blur, shadows, borders

### **🖼️ Overlay Features:**
✅ **PNG Transparency Support** - Perfect for logos and graphics
✅ **9 Position Options** - Complete grid positioning system
✅ **6 Animation Effects** - fade, slide-in, zoom-in, bounce, pulse, rotate
✅ **Custom Sizing** - width, height, scale control
✅ **Opacity Control** - 0-100% transparency
✅ **Precise Positioning** - offsetX, offsetY pixel-perfect placement
✅ **Remote & Local Images** - Support for URLs and local files
✅ **Multiple Overlays** - Add unlimited logos, buttons, watermarks

---

## 🔧 **Technical Details**

- **Built with:** Remotion + Node.js + Express
- **Video Processing:** Chrome Headless + FFmpeg-like rendering
- **File Storage:** Local renders/ directory + optional 0x0.st upload
- **CORS:** Enabled for cross-origin requests
- **Timeout:** 28 seconds for video loading/processing
