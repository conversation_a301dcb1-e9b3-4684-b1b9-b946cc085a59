# PowerShell script to test enhanced subtitles
$body = @{
    clips = @("https://0x0.st/8kgm.mp4")
    subtitles = @(
        @{
            text = "🌈 Rainbow Magic!"
            start = 0
            end = 3
            backgroundColor = "rainbow"
            textColor = "white"
            position = "top"
            transition = "bounce"
            transitionDuration = 0.8
        },
        @{
            text = "Custom gradient background"
            start = 4
            end = 7
            backgroundColor = "linear-gradient(135deg, #667eea, #764ba2)"
            position = "center"
            transition = "typewriter"
            transitionDuration = 2.0
        }
    )
} | ConvertTo-Json -Depth 10

Write-Host "Sending request to render enhanced subtitles..."
Write-Host "Request body:"
Write-Host $body

$response = Invoke-RestMethod -Uri "http://localhost:5001/render" -Method POST -Body $body -ContentType "application/json"

Write-Host "Response:"
$response | ConvertTo-Json -Depth 10

Write-Host "Job ID: $($response.id)"
Write-Host "Check status at: http://localhost:5001/status/$($response.id)"
