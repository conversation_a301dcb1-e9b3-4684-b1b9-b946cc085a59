const fs = require("fs");
const path = require("path");

function generateSubtitles(subtitles, outputDir) {
  const styled = subtitles.map((s, i) => ({
    id: `subtitle-${i}`,
    text: s.text,
    start: s.start,
    end: s.end,
    style: {
      fontSize: 60,
      color: "#ffffff",
      backgroundColor: "#000000aa",
      padding: 10,
      position: "bottomCenter",
    },
  }));

  const subtitlePath = path.join(outputDir, "subtitles.json");
  fs.writeFileSync(subtitlePath, JSON.stringify(styled, null, 2));
  return subtitlePath;
}

module.exports = { generateSubtitles };
