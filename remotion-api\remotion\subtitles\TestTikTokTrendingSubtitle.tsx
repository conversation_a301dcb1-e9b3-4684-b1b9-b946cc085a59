import React from "react";
import { AbsoluteFill, Sequence } from "remotion";
import { TikTokTrendingSubtitle } from "./TikTokTrendingSubtitle";

/**
 * Test composition for TikTokTrendingSubtitle
 * Renders a blank background with the subtitle in the center/bottom for preview/testing
 */
export const TestTikTokTrendingSubtitle: React.FC = () => {
  return (
    <AbsoluteFill style={{ background: "#222" }}>
      <Sequence from={15} durationInFrames={90}>
        <TikTokTrendingSubtitle
          text={"WHAT KIND OF\nFONT IS THIS?"}
          position="bottom"
          // backgroundColor can be omitted for default style
          animationFrame={15}
        />
      </Sequence>
    </AbsoluteFill>
  );
};
